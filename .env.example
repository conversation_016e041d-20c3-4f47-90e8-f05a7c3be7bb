# Environment Variables Template for BulldozerPriceGenius
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# Google Drive Model Storage Configuration
# Replace with your actual Google Drive file ID for the 560MB RandomForest model
GOOGLE_DRIVE_MODEL_ID=YOUR_GOOGLE_DRIVE_FILE_ID_HERE

# Streamlit Configuration
STREAMLIT_ENV=production

# Example Google Drive File ID format:
# GOOGLE_DRIVE_MODEL_ID=1ABC123DEF456GHI789JKL

# Instructions:
# 1. Upload your trained model file to Google Drive
# 2. Set sharing permissions to "Anyone with the link can view"
# 3. Get the shareable link
# 4. Extract the file ID from the link
# 5. Replace YOUR_GOOGLE_DRIVE_FILE_ID_HERE with your actual file ID
# 6. Save this file as .env (remove .example extension)
# 7. Add .env to your .gitignore file

# For Heroku deployment:
# heroku config:set GOOGLE_DRIVE_MODEL_ID=your_actual_file_id --app your-app-name

# For local development:
# Create .streamlit/secrets.toml with:
# GOOGLE_DRIVE_MODEL_ID = "your_actual_file_id"
