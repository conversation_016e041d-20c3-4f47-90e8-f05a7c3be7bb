# Timestamp
import datetime

import datetime
print(f"Notebook last run (end-to-end): {datetime.datetime.now()}")

import os

# Move to the desired directory
os.chdir('c:\\Users\\<USER>\\Dropbox\\1 PROJECT\\VS Code Project Respository\\About-BulldozerPriceGenius-_BPG-_v2')

# Get the current directory to verify the change
current_dir = os.getcwd()
current_dir

os.chdir(os.path.dirname(current_dir))
print("You set a new current directory")

import os

# Change the current working directory
os.chdir('c:\\Users\\<USER>\\Dropbox\\1 PROJECT\\VS Code Project Respository')

# Get the current working directory
current_dir = os.getcwd()
current_dir

# Import data analysis tools
import pandas as pd
import numpy as np
import matplotlib
import matplotlib.pyplot as plt


print(f"pandas version: {pd.__version__}")
print(f"NumPy version: {np.__version__}")
print(f"matplotlib version: {matplotlib.__version__}")

# This won't work since we've got missing numbers and categories
# from sklearn.ensemble import RandomForestRegressor

# model = RandomForestRegressor(n_jobs=-1)
# model.fit(X=df_tmp.drop("SalePrice", axis=1), # use all columns except SalePrice as X input
#          y=df_tmp.SalePrice) # use SalePrice column as y input

import os
import pandas as pd

# Define the file path
file_path = "C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_processed.csv"

# Check if the file exists
if os.path.exists(file_path):
    print(f"The file '{file_path}' exists.")
    # Load the CSV file into a DataFrame
    df_tmp = pd.read_csv(file_path)
    # Check for missing values and different datatypes
    df_tmp.info()
else:
    print(f"The file '{file_path}' does not exist.")

# Find missing values in the head of our DataFrame 
df_tmp.head().isna()

# Check for total missing values per column
df_tmp.isna().sum()

# Get the dtype of a given column
df_tmp["UsageBand"].dtype, df_tmp["UsageBand"].dtype.name

# Check whether a column is a string
pd.api.types.is_string_dtype(df_tmp["state"])

# Quick exampke of calling .items() on a dictionary
random_dict = {"key1": "All",
               "key2": "Good!"}

for key, value in random_dict.items():
    print(f"This is a key: {key}")
    print(f"This is a value: {value}")
    print()

# Print column names and example content of columns which contain strings
for label, content in df_tmp.items():
    if pd.api.types.is_string_dtype(content):
        # Check datatype of target column
        column_datatype = df_tmp[label].dtype.name

        # Get random sample from column values
        example_value = content.sample(1).values

        # Infer random sample datatype
        example_value_dtype = pd.api.types.infer_dtype(example_value)
        print(f"Column name: {label} | Column dtype: {column_datatype} | Example value: {example_value} | Example value dtype: {example_value_dtype}")

# Start a count of how many object type columns there are
number_of_object_type_columns = 0

for label, content in df_tmp.items():
    # Check to see if column is of object type (this will include the string columns)
    if pd.api.types.is_object_dtype(content): 
        # Check datatype of target column
        column_datatype = df_tmp[label].dtype.name

        # Get random sample from column values
        example_value = content.sample(1).values

        # Infer random sample datatype
        example_value_dtype = pd.api.types.infer_dtype(example_value)
        print(f"Column name: {label} | Column dtype: {column_datatype} | Example value: {example_value} | Example value dtype: {example_value_dtype}")

        number_of_object_type_columns += 1

print(f"\n[INFO] Total number of object type columns: {number_of_object_type_columns}")

# This will turn all of the object columns into category values
for label, content in df_tmp.items(): 
    if pd.api.types.is_object_dtype(content):
        df_tmp[label] = df_tmp[label].astype("category")

df_tmp.info()

# Check the datatype of a single column
df_tmp.state.dtype

# Get the category names of a given column
df_tmp.state.cat.categories

# Inspect the category codes
df_tmp.state.cat.codes

# Get example string using category number
target_state_cat_number = 39
target_state_cat_value = df_tmp.state.cat.categories[target_state_cat_number] 
print(f"[INFO] Target state category number {target_state_cat_number} maps to: {target_state_cat_value}")

import pandas as pd

# Assuming df_tmp is your preprocessed DataFrame
try:
    df_tmp.to_csv("C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_object_values_as_categories.csv",
                  index=False)
    print("SUCCESSFULLY SAVED! The data file 'TrainAndValid_object_values_as_categories.csv' has been successfully saved in data/processed.")
except Exception as e:
    print(f"An error occurred while saving the preprocessed data: {e}")