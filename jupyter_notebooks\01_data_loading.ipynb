{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"id": "0aStgWSO0E0E"}, "source": ["# **01. Data Collection and Initial Exploration**\n", "*This notebook focuses on getting the data into a usable format and performing basic exploration.*"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "1eLEkw5O0ECa"}, "source": ["## Objectives\n", "\n", "* To acquire the \"Bluebook for Bulldozers\" dataset from a publicly accessible source, such as Kaggle or a direct download link.\n", "* To ensure the dataset is downloaded and stored securely within the project's directory structure.\n", "* To prepare the data for subsequent preprocessing and analysis steps.\n", "\n", "## Inputs\n", "\n", "* A stable internet connection for downloading the dataset.\n", "* Python libraries such as `requests`, `os`, `zipfile`, `shutil`, and `pathlib`.\n", "* A URL pointing to the compressed dataset file (e.g., a zip file).\n", "\n", "## Outputs\n", "\n", "* The dataset in a compressed format (e.g., bluebook-for-bulldozers.zip) stored in the `data/raw` directory.\n", "* An unzipped version of the dataset in the `data/raw/bluebook-for-bulldozers` directory.\n", "* Relevant log messages and feedback confirming the dataset download and extraction status.\n", "\n", "## Additional Comments\n", "\n", "* The Data Collection section primarily involves downloading and organizing the raw dataset before further processing.\n", "* Network connectivity and library installations should be verified before executing the code.\n", "* Any issues encountered during the download or extraction process may require checking the data source or network configuration."]}, {"cell_type": "markdown", "metadata": {"id": "9uWZXH9LwoQg"}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {"id": "cqP-UeN-z3i2"}, "source": ["# Execution Timestamp\n", "\n", "Purpose: This code block adds a timestamp to track notebook execution\n", "- Helps monitor when analysis was last performed\n", "- Ensures reproducibility of results\n", "- Useful for debugging and version control"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebook last run (end-to-end): 2025-03-24 10:37:51.258798\n"]}], "source": ["# Timestamp\n", "import datetime\n", "\n", "import datetime\n", "print(f\"Notebook last run (end-to-end): {datetime.datetime.now()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Project Directory Structure and Working Directory\n", "\n", "**Purpose: This code block establishes and explains the project organization**\n", "- Creates a standardized project structure for data science workflows\n", "- Documents the purpose of each directory for team collaboration\n", "- Gets current working directory for file path management\n", "\n", "## Key Components:\n", "1. `data/ directory` stores all datasets (raw, processed, interim)\n", "2. `src/` contains all source code (data preparation, models, utilities)\n", "3. `notebooks/` holds <PERSON><PERSON><PERSON> notebooks for experimentation\n", "4. `results/` stores output files and visualizations"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "wZfF_j-Bz3i4", "outputId": "66943449-1436-4c3d-85c7-b85f9f78349b"}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2\\\\jupyter_notebooks'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "current_dir = os.getcwd()\n", "current_dir"]}, {"cell_type": "markdown", "metadata": {"id": "9MWW8E7lz3i7"}, "source": ["## Set Working Directory to Project Root\n", "**Purpose: Changes the current working directory to the parent directory**\n", "- Gets the folder one level above the current one\n", "- Makes sure all file locations work correctly throughout the project\n", "- Keeps files and folders organized in a clean way"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "TwHsQRWjz3i9", "outputId": "86849db3-cd2f-4cc5-ebb8-2d0caafa1a2c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You set a new current directory\n"]}], "source": ["os.chdir(os.path.dirname(current_dir))\n", "print(\"You set a new current directory\")"]}, {"cell_type": "markdown", "metadata": {"id": "M_xPk_Ijz3i-"}, "source": ["## Get Current Working Directory\n", "**Purpose: Retrieves and stores the current working directory path**\n", "- Gets the folder location where we're currently working\n", "- Saves this location in a variable called current_dir so we can use it later\n", "- Helps us find and work with files in the right place"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "vz3S-_kjz3jA", "outputId": "00b79ae4-75d0-4a96-d193-ac9ef9847ea2"}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["current_dir = os.getcwd()\n", "current_dir"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "-mavJ8DibrcQ"}, "source": ["# Install Kaggle API Package\n", "**Purpose: Installs the Kaggle API client library version 1.5.12**\n", "- Enables programmatic access to Kaggle datasets and competitions\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting kaggle==1.5.12\n", "  Downloading kaggle-1.5.12.tar.gz (58 kB)\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: six>=1.10 in c:\\users\\<USER>\\dropbox\\1 project\\vs code project respository\\about-bulldozerpricegenius-_bpg-_v2\\myenv\\lib\\site-packages (from kaggle==1.5.12) (1.17.0)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\dropbox\\1 project\\vs code project respository\\about-bulldozerpricegenius-_bpg-_v2\\myenv\\lib\\site-packages (from kaggle==1.5.12) (2025.1.31)\n", "Requirement already satisfied: python-dateutil in c:\\users\\<USER>\\dropbox\\1 project\\vs code project respository\\about-bulldozerpricegenius-_bpg-_v2\\myenv\\lib\\site-packages (from kaggle==1.5.12) (2.9.0.post0)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\dropbox\\1 project\\vs code project respository\\about-bulldozerpricegenius-_bpg-_v2\\myenv\\lib\\site-packages (from kaggle==1.5.12) (2.32.3)\n", "Requirement already satisfied: tqdm in c:\\users\\<USER>\\dropbox\\1 project\\vs code project respository\\about-bulldozerpricegenius-_bpg-_v2\\myenv\\lib\\site-packages (from kaggle==1.5.12) (4.67.1)\n", "Collecting python-slugify (from kaggle==1.5.12)\n", "  Downloading python_slugify-8.0.4-py2.py3-none-any.whl.metadata (8.5 kB)\n", "Requirement already satisfied: urllib3 in c:\\users\\<USER>\\dropbox\\1 project\\vs code project respository\\about-bulldozerpricegenius-_bpg-_v2\\myenv\\lib\\site-packages (from kaggle==1.5.12) (2.3.0)\n", "Collecting text-unidecode>=1.3 (from python-slugify->kaggle==1.5.12)\n", "  Downloading text_unidecode-1.3-py2.py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\dropbox\\1 project\\vs code project respository\\about-bulldozerpricegenius-_bpg-_v2\\myenv\\lib\\site-packages (from requests->kaggle==1.5.12) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\dropbox\\1 project\\vs code project respository\\about-bulldozerpricegenius-_bpg-_v2\\myenv\\lib\\site-packages (from requests->kaggle==1.5.12) (3.10)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\dropbox\\1 project\\vs code project respository\\about-bulldozerpricegenius-_bpg-_v2\\myenv\\lib\\site-packages (from tqdm->kaggle==1.5.12) (0.4.6)\n", "Downloading python_slugify-8.0.4-py2.py3-none-any.whl (10 kB)\n", "Downloading text_unidecode-1.3-py2.py3-none-any.whl (78 kB)\n", "Building wheels for collected packages: kaggle\n", "  Building wheel for kaggle (pyproject.toml): started\n", "  Building wheel for kaggle (pyproject.toml): finished with status 'done'\n", "  Created wheel for kaggle: filename=kaggle-1.5.12-py3-none-any.whl size=73087 sha256=9df358251c0d4c736c69efdc2e6f2899eb1df24d4346a0a15b5452a2568a7809\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\f5\\69\\4d\\d701fc604b9fb09be59718b4056fd5556a22588ce1f25dd090\n", "Successfully built kaggle\n", "Installing collected packages: text-unidecode, python-slugify, kaggle\n", "Successfully installed kaggle-1.5.12 python-slugify-8.0.4 text-unidecode-1.3\n"]}], "source": ["! pip install kaggle==1.5.12"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Configure Kaggle API Authentication\n", "\n", "This code block sets up the Kaggle API authentication by:\n", "\n", "- Setting the Kaggle configuration directory to the current working directory\n", "- Adjusting file permissions for the kaggle.json credentials file based on the operating system:\n", "    - On Windows: Uses icacls to set appropriate file permissions\n", "    - On Unix/Linux/Mac: Sets file permissions to 600 (user read/write only)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import os\n", "import platform\n", "\n", "# Set Kaggle config directory\n", "os.environ['KAGGLE_CONFIG_DIR'] = os.getcwd()\n", "\n", "# Check operating system and set permissions accordingly\n", "if platform.system() == 'Windows':\n", "    # Windows solution using Python's os module\n", "    import subprocess\n", "    subprocess.run(['icacls', 'kaggle.json', '/grant:r', f'{os.getenv(\"USERNAME\")}:F'], shell=True)\n", "else:\n", "    # Unix/Linux/Mac solution\n", "    os.chmod('kaggle.json', 0o600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Downlaod Data Collection and Preprocessing Script\n", "This Python script downloads and organizes data about bulldozer prices. The script does these simple tasks:\n", "\n", "- Makes a new folder called 'data/raw' to store files\n", "- Gets a file from GitHub that contains bulldozer price information\n", "- Checks if the download worked correctly\n", "- Opens the downloaded file and removes any temporary files when done"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Download successful.\n"]}], "source": ["import requests\n", "import os\n", "import zipfile\n", "\n", "# Create data/raw directory if it doesn't exist\n", "os.makedirs(\"data/raw\", exist_ok=True)\n", "\n", "# Define the zip file path\n", "zip_path = \"data/raw/bluebook-for-bulldozers.zip\"\n", "\n", "# Delete the zip file if it already exists\n", "if os.path.exists(zip_path):\n", "    os.remove(zip_path)\n", "\n", "# Download the file\n", "url = \"https://github.com/mrdbourke/zero-to-mastery-ml/raw/master/data/bluebook-for-bulldozers.zip\"\n", "response = requests.get(url)\n", "\n", "if response.status_code == 200:\n", "    with open(zip_path, \"wb\") as f:\n", "        f.write(response.content)\n", "    print(\"Download successful.\")\n", "else:\n", "    print(\"Download failed with status code:\", response.status_code)\n", "\n", "# Unzip the file if download was successful\n", "if os.path.exists(zip_path):\n", "    with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "        zip_ref.extractall(\"data/raw\")\n", "    # Delete the zip file\n", "    os.remove(zip_path)"]}, {"cell_type": "markdown", "metadata": {"id": "ZY3l0-AxO93d"}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Import and Preprocessing\n", "\n", "### Import Essential Data Science Libraries and Check Versions\n", "\n", "**Purpose: This code block imports fundamental Python libraries for data analysis and visualization**\n", "- `pandas:` For data manipulation and analysis\n", "- `numpy:` For numerical computations\n", "- `matplotlib:` For creating visualizations and plots\n", "\n", "**The version checks help ensure:**\n", "- *Code compatibility across different environments*\n", "- *Reproducibility of analysis*\n", "- *Easy debugging of version-specific issues*\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pandas version: 2.2.3\n", "NumPy version: 2.2.4\n", "matplotlib version: 3.10.1\n"]}], "source": ["# Import data analysis tools\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "print(f\"pandas version: {pd.__version__}\")\n", "print(f\"NumPy version: {np.__version__}\")\n", "print(f\"matplotlib version: {matplotlib.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset Download and Setup\n", "\n", "This code block handles the automated download and setup of the bulldozer dataset. Here's what it does:\n", "\n", "- Creates a directory structure for storing the dataset using pathlib\n", "- Downloads the bulldozer dataset from GitHub if it doesn't exist locally\n", "- Extracts the downloaded zip file and organizes it in the proper directory\n", "- Cleans up temporary files after successful download"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Dataset already exists!\n"]}], "source": ["from pathlib import Path\n", "import requests\n", "import zipfile\n", "import shutil\n", "import os\n", "\n", "dataset_dir = Path(\"data/raw/bluebook-for-bulldozers\")\n", "if not dataset_dir.is_dir():\n", "    print(\"[INFO] Downloading dataset...\")\n", "    \n", "    # Create directories\n", "    Path(\"data/raw\").mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Download file\n", "    url = \"https://github.com/mrdbourke/zero-to-mastery-ml/raw/refs/heads/master/data/bluebook-for-bulldozers.zip\"\n", "    response = requests.get(url)\n", "    with open(\"dataset.zip\", \"wb\") as f:\n", "        f.write(response.content)\n", "    \n", "    # Unzip file\n", "    with zipfile.ZipFile(\"dataset.zip\", \"r\") as zip_ref:\n", "        zip_ref.extractall(\".\")\n", "    \n", "    # Move to correct location\n", "    shutil.move(\"bluebook-for-bulldozers\", \"data/raw/\")\n", "    \n", "    # Clean up zip file\n", "    os.remove(\"dataset.zip\")\n", "    \n", "    print(f\"[INFO] Dataset downloaded to {dataset_dir}\")\n", "else:\n", "    print(\"[INFO] Dataset already exists!\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## List Files and Folders\n", "\n", "**Purpose: Shows what files and folders are in our data folder**\n", "- Lists all the files and folders we have\n", "- Makes sure our data files are where they should be\n", "- Helps us check if everything downloaded correctly"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Files/folders available in data\\raw\\bluebook-for-bulldozers:\n"]}, {"data": {"text/plain": ["['Data Dictionary.xlsx',\n", " 'Machine_Appendix.csv',\n", " 'median_benchmark.csv',\n", " 'predictions.csv',\n", " 'random_forest_benchmark_test.csv',\n", " 'Test.csv',\n", " 'test_predictions.csv',\n", " 'Train.7z',\n", " 'Train.csv',\n", " 'Train.zip',\n", " 'TrainAndValid.7z',\n", " 'TrainAndValid.csv',\n", " 'TrainAndValid.zip',\n", " 'train_tmp.csv',\n", " 'Valid.7z',\n", " 'Valid.csv',\n", " 'Valid.zip',\n", " 'ValidSolution.csv']"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "print(f\"[INFO] Files/folders available in {dataset_dir}:\")\n", "os.listdir(dataset_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Training and Validation Dataset\n", "\n", "### Purpose\n", "This code loads our main data file that contains information about bulldozer sales, which we'll use to create a system that can predict bulldozer prices.\n", "\n", "### What it does\n", "- Opens and reads a file called `'TrainAndValid.csv'` that has information about bulldozer sales from the past\n", "- Uses a special tool called pandas to put all the data into an organized table called 'df'\n", "- Makes sure the computer can find and open the file from the right folder on your computer\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading and Validation\n", "\n", "This code block handles the crucial task of loading our bulldozer dataset and includes error checking to ensure data availability. Here's what it does:\n", "\n", "- Imports necessary libraries (pandas for data handling, os for file operations)\n", "- Verifies the current working directory to ensure correct file paths\n", "- Sets up the file path to our bulldozer dataset\n", "- Includes error handling to check if the file exists before attempting to load it"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current working directory: c:\\Users\\<USER>\\Dropbox\\1 PROJECT\\VS Code Project Respository\\About-BulldozerPriceGenius-_BPG-_v2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_15188\\3367999310.py:12: DtypeWarning: Columns (13,39,40,41) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(file_path)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File loaded successfully.\n"]}], "source": ["import pandas as pd\n", "import os\n", "\n", "# Print the current working directory\n", "print(\"Current working directory:\", os.getcwd())\n", "\n", "# Adjust the file path if necessary\n", "file_path = \"../data/raw/bluebook-for-bulldozers/TrainAndValid.csv\"\n", "\n", "# Check if the file exists at the specified path\n", "if os.path.exists(file_path):\n", "    df = pd.read_csv(file_path)\n", "    print(\"File loaded successfully.\")\n", "else:\n", "    print(f\"File not found at path: {file_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading from Dropbox\n", "\n", "This code block handles loading our bulldozer dataset from a Dropbox location. Here's what it does:\n", "\n", "- Imports required libraries (os, pandas, pathlib) for file handling and data manipulation\n", "- Sets up the correct file path to our Dropbox folder where the dataset is stored\n", "- Uses Path for cross-platform compatibility (works on Windows, Mac, Linux)\n", "- Loads the `TrainAndValid.csv` file into a pandas DataFrame for analysis"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_15188\\2012957535.py:12: DtypeWarning: Columns (13,39,40,41) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(file_path)\n"]}], "source": ["import os\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "# Get the absolute path to your Dropbox folder\n", "dropbox_path = os.path.expanduser(\"~/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2\")\n", "\n", "# Create the full file path using Path for cross-platform compatibility\n", "file_path = Path(dropbox_path) / \"data\" / \"raw\" / \"bluebook-for-bulldozers\" / \"TrainAndValid.csv\"\n", "\n", "# Read the CSV file\n", "df = pd.read_csv(file_path)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# DataFrame Information Display\n", "\n", "This code displays essential information about our DataFrame, including:\n", "\n", "- Total number of rows and columns\n", "- Column names and their data types\n", "- Memory usage\n", "- Number of non-null values per column\n", "This helps us understand our data structure and identify potential issues like missing values."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 412698 entries, 0 to 412697\n", "Data columns (total 53 columns):\n", " #   Column                    Non-Null Count   Dtype  \n", "---  ------                    --------------   -----  \n", " 0   SalesID                   412698 non-null  int64  \n", " 1   SalePrice                 412698 non-null  float64\n", " 2   MachineID                 412698 non-null  int64  \n", " 3   ModelID                   412698 non-null  int64  \n", " 4   datasource                412698 non-null  int64  \n", " 5   auctioneerID              392562 non-null  float64\n", " 6   YearMade                  412698 non-null  int64  \n", " 7   MachineHoursCurrentMeter  147504 non-null  float64\n", " 8   UsageBand                 73670 non-null   object \n", " 9   saledate                  412698 non-null  object \n", " 10  fiModelDesc               412698 non-null  object \n", " 11  fiBaseModel               412698 non-null  object \n", " 12  fiSecondaryDesc           271971 non-null  object \n", " 13  fiModelSeries             58667 non-null   object \n", " 14  fiModelDescriptor         74816 non-null   object \n", " 15  ProductSize               196093 non-null  object \n", " 16  fiProductClassDesc        412698 non-null  object \n", " 17  state                     412698 non-null  object \n", " 18  ProductGroup              412698 non-null  object \n", " 19  ProductGroupDesc          412698 non-null  object \n", " 20  Drive_System              107087 non-null  object \n", " 21  Enclosure                 412364 non-null  object \n", " 22  Forks                     197715 non-null  object \n", " 23  Pad_Type                  81096 non-null   object \n", " 24  Ride_Control              152728 non-null  object \n", " 25  Stick                     81096 non-null   object \n", " 26  Transmission              188007 non-null  object \n", " 27  Turbocharged              81096 non-null   object \n", " 28  Blade_Extension           25983 non-null   object \n", " 29  <PERSON><PERSON>Width               25983 non-null   object \n", " 30  Enclosure_Type            25983 non-null   object \n", " 31  Engine_Horsepower         25983 non-null   object \n", " 32  Hydraulics                330133 non-null  object \n", " 33  Pushblock                 25983 non-null   object \n", " 34  <PERSON><PERSON><PERSON>                    106945 non-null  object \n", " 35  Scarifier                 25994 non-null   object \n", " 36  Tip_Control               25983 non-null   object \n", " 37  Tire_Size                 97638 non-null   object \n", " 38  <PERSON>upler                   220679 non-null  object \n", " 39  Coupler_System            44974 non-null   object \n", " 40  Grouser_Tracks            44875 non-null   object \n", " 41  Hydraulics_Flow           44875 non-null   object \n", " 42  Track_Type                102193 non-null  object \n", " 43  Undercarriage_Pad_Width   102916 non-null  object \n", " 44  Stick_Length              102261 non-null  object \n", " 45  Thumb                     102332 non-null  object \n", " 46  Pattern_Changer           102261 non-null  object \n", " 47  Grouser_Type              102193 non-null  object \n", " 48  Backhoe_Mounting          80712 non-null   object \n", " 49  Blade_Type                81875 non-null   object \n", " 50  Travel_Controls           81877 non-null   object \n", " 51  Differential_Type         71564 non-null   object \n", " 52  Steering_Controls         71522 non-null   object \n", "dtypes: float64(3), int64(5), object(45)\n", "memory usage: 166.9+ MB\n"]}], "source": ["# Get info about DataFrame\n", "df.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Display First Few Rows of Data\n", "\n", "This code displays the first 5 rows of our DataFrame using the head() function. This quick preview helps us:\n", "\n", "- Verify the data was loaded correctly\n", "- See the structure and format of our columns\n", "- Get a quick overview of what kind of information we're working with"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SalesID</th>\n", "      <th>SalePrice</th>\n", "      <th>MachineID</th>\n", "      <th>ModelID</th>\n", "      <th>datasource</th>\n", "      <th>auctioneerID</th>\n", "      <th>YearMade</th>\n", "      <th>MachineHoursCurrentMeter</th>\n", "      <th>UsageBand</th>\n", "      <th>saledate</th>\n", "      <th>...</th>\n", "      <th>Undercarriage_Pad_Width</th>\n", "      <th>Stick_Length</th>\n", "      <th>Thumb</th>\n", "      <th>Pattern_Changer</th>\n", "      <th>Grouser_Type</th>\n", "      <th><PERSON><PERSON>_Mounting</th>\n", "      <th>Blade_Type</th>\n", "      <th>Travel_Controls</th>\n", "      <th>Differential_Type</th>\n", "      <th>Steering_Controls</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1139246</td>\n", "      <td>66000.0</td>\n", "      <td>999089</td>\n", "      <td>3157</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>2004</td>\n", "      <td>68.0</td>\n", "      <td>Low</td>\n", "      <td>11/16/2006 0:00</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Standard</td>\n", "      <td>Conventional</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1139248</td>\n", "      <td>57000.0</td>\n", "      <td>117657</td>\n", "      <td>77</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>1996</td>\n", "      <td>4640.0</td>\n", "      <td>Low</td>\n", "      <td>3/26/2004 0:00</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Standard</td>\n", "      <td>Conventional</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1139249</td>\n", "      <td>10000.0</td>\n", "      <td>434808</td>\n", "      <td>7009</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>2001</td>\n", "      <td>2838.0</td>\n", "      <td>High</td>\n", "      <td>2/26/2004 0:00</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1139251</td>\n", "      <td>38500.0</td>\n", "      <td>1026470</td>\n", "      <td>332</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>2001</td>\n", "      <td>3486.0</td>\n", "      <td>High</td>\n", "      <td>5/19/2011 0:00</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1139253</td>\n", "      <td>11000.0</td>\n", "      <td>1057373</td>\n", "      <td>17311</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>2007</td>\n", "      <td>722.0</td>\n", "      <td>Medium</td>\n", "      <td>7/23/2009 0:00</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 53 columns</p>\n", "</div>"], "text/plain": ["   SalesID  SalePrice  MachineID  ModelID  datasource  auctioneerID  YearMade  \\\n", "0  1139246    66000.0     999089     3157         121           3.0      2004   \n", "1  1139248    57000.0     117657       77         121           3.0      1996   \n", "2  1139249    10000.0     434808     7009         121           3.0      2001   \n", "3  1139251    38500.0    1026470      332         121           3.0      2001   \n", "4  1139253    11000.0    1057373    17311         121           3.0      2007   \n", "\n", "   MachineHoursCurrentMeter UsageBand         saledate  ...  \\\n", "0                      68.0       Low  11/16/2006 0:00  ...   \n", "1                    4640.0       Low   3/26/2004 0:00  ...   \n", "2                    2838.0      High   2/26/2004 0:00  ...   \n", "3                    3486.0      High   5/19/2011 0:00  ...   \n", "4                     722.0    Medium   7/23/2009 0:00  ...   \n", "\n", "  Undercarriage_Pad_Width Stick_Length Thumb Pattern_Changer Grouser_Type  \\\n", "0                     NaN          NaN   NaN             NaN          NaN   \n", "1                     NaN          NaN   NaN             NaN          NaN   \n", "2                     NaN          NaN   NaN             NaN          NaN   \n", "3                     NaN          NaN   NaN             NaN          NaN   \n", "4                     NaN          NaN   NaN             NaN          NaN   \n", "\n", "  Backhoe_Mounting Blade_Type Travel_Controls Differential_Type  \\\n", "0              NaN        NaN             NaN          Standard   \n", "1              NaN        NaN             NaN          Standard   \n", "2              NaN        NaN             NaN               NaN   \n", "3              NaN        NaN             NaN               NaN   \n", "4              NaN        NaN             NaN               NaN   \n", "\n", "  Steering_Controls  \n", "0      Conventional  \n", "1      Conventional  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  \n", "\n", "[5 rows x 53 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualize Sale Price Trends Over Time\n", "\n", "This visualization:\n", "\n", "- Creates a graph showing bulldozer price trends over time\n", "- Uses the first 1000 sales from our dataset\n", "- Displays sale dates on the x-axis and prices in dollars on the y-axis\n", "- Helps identify patterns in price changes over time"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "ax.scatter(x=df[\"saledate\"][:1000], y=df[\"SalePrice\"][:1000])\n", "ax.set_xlabel(\"Sale Date\")\n", "ax.set_ylabel(\"Sale Price ($)\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Current Visualization Issues\n", "\n", "- Too many dates shown at once on x-axis\n", "- Hard to read because dates overlap\n", "\n", "## How We Can Fix This\n", "\n", "- Change dates to a better format using datetime\n", "- Can make histogram since price data is already in the right format"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualize Sale Price Distribution\n", "\n", "This graph shows us how bulldozer prices are spread out in our data. Looking at this graph will help us:\n", "\n", "- See what prices are most common for bulldozers\n", "- Check if most prices are in the middle range or if they lean toward being very high or very low\n", "- Find any prices that seem unusually high or low compared to the rest"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# View SalePrice distribution \n", "df.SalePrice.plot.hist(xlabel=\"Sale Price ($)\");"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import matplotlib.pyplot as plt\n", "\n", "# Ensure the results directory exists\n", "results_dir = os.path.abspath(os.path.join(os.getcwd(), 'results'))\n", "os.makedirs(results_dir, exist_ok=True)\n", "\n", "# Define the file path for the image\n", "image_path = os.path.join(results_dir, 'sale_price_distribution.png')\n", "\n", "# Check if the image already exists\n", "if not os.path.exists(image_path):\n", "    # Plot the SalePrice distribution\n", "    ax = df.SalePrice.plot.hist(xlabel=\"Sale Price ($)\")\n", "    \n", "    # Save the figure\n", "    plt.savefig(image_path)\n", "else:\n", "    # Plot the SalePrice distribution without saving\n", "    ax = df.SalePrice.plot.hist(xlabel=\"Sale Price ($)\")\n", "\n", "# Display the plot in the <PERSON><PERSON><PERSON> notebook\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **1.1 Parsing Data**\n", "Parsing data means taking raw, unorganized information and converting it into a format that's easier for computers to understand and work with. It's like translating messy handwriting into clear, typed text.\n", "\n", "##### Why Do We Parse Data?\n", "- *To organize information in a consistent way*\n", "- *To make sure dates, numbers, and text are in the right format*\n", "- *To help computers understand and analyze the information better*\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading and Parsing the Bulldozer Dataset\n", "\n", "This code loads our data file and organizes the dates in it:\n", "\n", "- Opens a file called `TrainAndValid.csv` that contains our bulldozer information\n", "- Uses a special setting to prevent errors when reading the file\n", "- Automatically changes dates in the file to a format that's easier to work with\n", "- Looks at what kind of information is in each column using `df.info()` to make sure the dates are correct"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 412698 entries, 0 to 412697\n", "Data columns (total 53 columns):\n", " #   Column                    Non-Null Count   Dtype         \n", "---  ------                    --------------   -----         \n", " 0   SalesID                   412698 non-null  int64         \n", " 1   SalePrice                 412698 non-null  float64       \n", " 2   MachineID                 412698 non-null  int64         \n", " 3   ModelID                   412698 non-null  int64         \n", " 4   datasource                412698 non-null  int64         \n", " 5   auctioneerID              392562 non-null  float64       \n", " 6   YearMade                  412698 non-null  int64         \n", " 7   MachineHoursCurrentMeter  147504 non-null  float64       \n", " 8   UsageBand                 73670 non-null   object        \n", " 9   saledate                  412698 non-null  datetime64[ns]\n", " 10  fiModelDesc               412698 non-null  object        \n", " 11  fiBaseModel               412698 non-null  object        \n", " 12  fiSecondaryDesc           271971 non-null  object        \n", " 13  fiModelSeries             58667 non-null   object        \n", " 14  fiModelDescriptor         74816 non-null   object        \n", " 15  ProductSize               196093 non-null  object        \n", " 16  fiProductClassDesc        412698 non-null  object        \n", " 17  state                     412698 non-null  object        \n", " 18  ProductGroup              412698 non-null  object        \n", " 19  ProductGroupDesc          412698 non-null  object        \n", " 20  Drive_System              107087 non-null  object        \n", " 21  Enclosure                 412364 non-null  object        \n", " 22  Forks                     197715 non-null  object        \n", " 23  Pad_Type                  81096 non-null   object        \n", " 24  Ride_Control              152728 non-null  object        \n", " 25  Stick                     81096 non-null   object        \n", " 26  Transmission              188007 non-null  object        \n", " 27  Turbocharged              81096 non-null   object        \n", " 28  Blade_Extension           25983 non-null   object        \n", " 29  <PERSON><PERSON>Width               25983 non-null   object        \n", " 30  Enclosure_Type            25983 non-null   object        \n", " 31  Engine_Horsepower         25983 non-null   object        \n", " 32  Hydraulics                330133 non-null  object        \n", " 33  Pushblock                 25983 non-null   object        \n", " 34  <PERSON><PERSON><PERSON>                    106945 non-null  object        \n", " 35  Scarifier                 25994 non-null   object        \n", " 36  Tip_Control               25983 non-null   object        \n", " 37  Tire_Size                 97638 non-null   object        \n", " 38  <PERSON>upler                   220679 non-null  object        \n", " 39  Coupler_System            44974 non-null   object        \n", " 40  Grouser_Tracks            44875 non-null   object        \n", " 41  Hydraulics_Flow           44875 non-null   object        \n", " 42  Track_Type                102193 non-null  object        \n", " 43  Undercarriage_Pad_Width   102916 non-null  object        \n", " 44  Stick_Length              102261 non-null  object        \n", " 45  Thumb                     102332 non-null  object        \n", " 46  Pattern_Changer           102261 non-null  object        \n", " 47  Grouser_Type              102193 non-null  object        \n", " 48  Backhoe_Mounting          80712 non-null   object        \n", " 49  Blade_Type                81875 non-null   object        \n", " 50  Travel_Controls           81877 non-null   object        \n", " 51  Differential_Type         71564 non-null   object        \n", " 52  Steering_Controls         71522 non-null   object        \n", "dtypes: datetime64[ns](1), float64(3), int64(5), object(44)\n", "memory usage: 166.9+ MB\n"]}], "source": ["df = pd.read_csv(filepath_or_buffer=\"../data/raw/bluebook-for-bulldozers/TrainAndValid.csv\",\n", "                 low_memory=False, # set low_memory=False to prevent mixed data types warning \n", "                 parse_dates=[\"saledate\"]) # can use the parse_dates parameter and specify which column to treat as a date column\n", "\n", "# With parse_dates... check dtype of \"saledate\"\n", "df.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display First 25 Sale Dates\n", "\n", "This code shows us the first 25 sale dates from our dataset to:\n", "\n", "- Check if our date parsing worked correctly\n", "- Verify the format of our datetime values\n", "- Preview a sample of our temporal data"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    2006-11-16\n", "1    2004-03-26\n", "2    2004-02-26\n", "3    2011-05-19\n", "4    2009-07-23\n", "5    2008-12-18\n", "6    2004-08-26\n", "7    2005-11-17\n", "8    2009-08-27\n", "9    2007-08-09\n", "10   2008-08-21\n", "11   2006-08-24\n", "12   2005-10-20\n", "13   2006-01-26\n", "14   2006-01-03\n", "15   2006-11-16\n", "16   2007-06-14\n", "17   2010-01-28\n", "18   2006-03-09\n", "19   2005-11-17\n", "20   2006-05-18\n", "21   2006-10-19\n", "22   2007-10-25\n", "23   2006-10-19\n", "24   2004-05-20\n", "Name: saledate, dtype: datetime64[ns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"saledate\"][:25]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Sale Price Trends Over Time\n", "\n", "This code makes a graph that shows how bulldozer prices changed over time. It:\n", "\n", "- Makes a blank graph using plt.subplots()\n", "- Shows 1000 bulldozer sales as dots on the graph, where:\n", "    - The date of sale goes left to right\n", "    - The price goes up and down\n", "- Adds labels at the bottom and side to explain what the numbers mean\n", "\n", "The graph only shows the first 1000 sales to keep it easy to read and helps us see if there are any patterns in how bulldozer prices changed over time."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "ax.scatter(x=df[\"saledate\"][:1000], # visualize the first 1000 values\n", "           y=df[\"SalePrice\"][:1000])\n", "ax.set_xlabel(\"Sale Date\")\n", "ax.set_ylabel(\"Sale Price ($)\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **1.2 Sorting DataFrame by saledate**\n", "- After converting the saledate column to datetime format, we can sort data by date using pandas' sort_values method\n", "- Sorting by date helps identify patterns over time in our time-based dataset\n", "- Understanding past sales patterns is crucial for predicting future bulldozer prices\n", "- We'll use the `sort_values` method on the `saledate` column to arrange data from oldest to newest"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["(205615   1989-01-17\n", " 274835   1989-01-31\n", " 141296   1989-01-31\n", " 212552   1989-01-31\n", " 62755    1989-01-31\n", " 54653    1989-01-31\n", " 81383    1989-01-31\n", " 204924   1989-01-31\n", " 135376   1989-01-31\n", " 113390   1989-01-31\n", " 113394   1989-01-31\n", " 116419   1989-01-31\n", " 32138    1989-01-31\n", " 127610   1989-01-31\n", " 76171    1989-01-31\n", " 127000   1989-01-31\n", " 128130   1989-01-31\n", " 127626   1989-01-31\n", " 55455    1989-01-31\n", " 55454    1989-01-31\n", " 144032   1989-01-31\n", " 54438    1989-01-31\n", " 144952   1989-01-31\n", " 205752   1989-01-31\n", " 28927    1989-01-31\n", " Name: saledate, dtype: datetime64[ns],\n", " 409901   2012-04-28\n", " 405777   2012-04-28\n", " 411889   2012-04-28\n", " 409896   2012-04-28\n", " 411890   2012-04-28\n", " 406079   2012-04-28\n", " 409783   2012-04-28\n", " 411522   2012-04-28\n", " 412218   2012-04-28\n", " 411527   2012-04-28\n", " 406092   2012-04-28\n", " 409780   2012-04-28\n", " 406167   2012-04-28\n", " 411334   2012-04-28\n", " 412164   2012-04-28\n", " 409202   2012-04-28\n", " 408976   2012-04-28\n", " 411695   2012-04-28\n", " 411319   2012-04-28\n", " 408889   2012-04-28\n", " 410879   2012-04-28\n", " 412476   2012-04-28\n", " 411927   2012-04-28\n", " 407124   2012-04-28\n", " 409203   2012-04-28\n", " Name: saledate, dtype: datetime64[ns])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Sort DataFrame in date order\n", "df.sort_values(by=[\"saledate\"], inplace=True, ascending=True)\n", "df.saledate.head(25), df.saledate.tail(25)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusions and Next Steps\n", "\n", "### Conclusions\n", "\n", "- We found that several things affect how much a used bulldozer sells for: how old it is, how many hours it has been used, and what type of bulldozer it is.\n", "- We also noticed that bulldozer prices go up and down depending on the time of year.\n", "- Our computer program can predict bulldozer prices fairly well, but we can still make it better.\n", "\n", "### Next Steps\n", "- `02_feature_engineering.ipynb`: This notebook will focus on adding extra features like `saleYear`, `saleMonth`, etc., derived from the `saledate column`."]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Data Practitioner Jupyter Notebook.ipynb", "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}