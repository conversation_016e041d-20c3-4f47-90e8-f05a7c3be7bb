{"cells": [{"cell_type": "markdown", "metadata": {"id": "0aStgWSO0E0E"}, "source": ["# **04. Data Preprocessing**\n", "\n", "*This notebook will handle converting data types, addressing missing values, and preparing the data for modeling.*"]}, {"cell_type": "markdown", "metadata": {"id": "1eLEkw5O0ECa"}, "source": ["## Objectives\n", "\n", "* Clean and organize the bulldozer price data for analysis by:\n", "    - Filling in missing information\n", "    - Converting text data into numbers\n", "    - Adjusting number values for better analysis\n", "\n", "## Inputs\n", "\n", "- Raw bulldozer price data file (e.g., `bulldozer_data.csv`)\n", "- Configuration file for preprocessing parameters (optional) \n", "\n", "## Outputs\n", "\n", "- Cleaned and preprocessed data file (e.g., `preprocessed_bulldozer_data.csv`)\n", "- Summary statistics and visualizations of the preprocessing steps\n", "\n", "## Additional Comments\n", "- Ensure that the preprocessing steps are reproducible and well-documented.\n", "- It is strongly recommended to save your work at regular intervals to help troubleshoot any issues that may arise.\n", "\n", "##### **Traditional Data Analysis Techniques**\n", "\n", "- Descriptive statistics to understand the distribution of features\n", "- Handling missing values using mean/mode imputation or removal\n", "- Encoding categorical variables using one-hot encoding or label encoding\n", "\n", "##### **Machine Learning Techniques**\n", "\n", "- Adjusting the data values to a common scale\n", "- Using techniques to reduce the number of data features when needed (like PCA, which helps simplify complex data)\n", "- Splitting the data into training and testing sets for model evaluation\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "9uWZXH9LwoQg"}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {"id": "cqP-UeN-z3i2"}, "source": ["# Execution Timestamp\n", "\n", "Purpose: This code block adds a timestamp to track notebook execution\n", "- Helps monitor when analysis was last performed\n", "- Ensures reproducibility of results\n", "- Useful for debugging and version control"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebook last run (end-to-end): 2025-03-24 10:30:09.299776\n"]}], "source": ["# Timestamp\n", "import datetime\n", "\n", "import datetime\n", "print(f\"Notebook last run (end-to-end): {datetime.datetime.now()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Project Directory Structure and Working Directory\n", "\n", "**Purpose: This code block establishes and explains the project organization**\n", "- Creates a standardized project structure for data science workflows\n", "- Documents the purpose of each directory for team collaboration\n", "- Gets current working directory for file path management\n", "\n", "## Key Components:\n", "1. `data/ directory` stores all datasets (raw, processed, interim)\n", "2. `src/` contains all source code (data preparation, models, utilities)\n", "3. `notebooks/` holds <PERSON><PERSON><PERSON> notebooks for experimentation\n", "4. `results/` stores output files and visualizations\n", "\n", "## Project Root Structure\n", "\n", "- **`data/`** - Where all your datasets live\n", "    - `raw/` - Original, untouched data\n", "    - `processed/` - Cleaned and prepared data\n", "    - `interim/` - Temporary data files\n", "- **`src/`** - Your source code\n", "    - `data_prep/` - Code for preparing data\n", "    - `models/` - Your ML models\n", "    - `utils/` - Helper functions\n", "- **`notebooks/`** - <PERSON><PERSON><PERSON> notebooks for experiments\n", "- **`results/`** - Model outputs and visualizations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up Working Directory\n", "This code block sets up the working environment by:\n", "- Changing to the project directory where our code and data files are located\n", "- Verifying the current working directory to ensure we're in the right place"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "# Move to the desired directory\n", "os.chdir('c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2')\n", "\n", "# Get the current directory to verify the change\n", "current_dir = os.getcwd()\n", "current_dir"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set Working Directory to Project Root\n", "**Purpose: Changes the current working directory to the parent directory**\n", "- Gets the folder one level above the current one\n", "- Makes sure all file locations work correctly throughout the project\n", "- Keeps files and folders organized in a clean way"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You set a new current directory\n"]}], "source": ["os.chdir(os.path.dirname(current_dir))\n", "print(\"You set a new current directory\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get Current Working Directory\n", "**Purpose: Retrieves and stores the current working directory path**\n", "- Gets the folder location where we're currently working\n", "- Saves this location in a variable called current_dir so we can use it later\n", "- Helps us find and work with files in the right place"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "# Change the current working directory\n", "os.chdir('c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository')\n", "\n", "# Get the current working directory\n", "current_dir = os.getcwd()\n", "current_dir"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Import Essential Data Science Libraries and Check Versions**\n", "\n", "**Purpose: This code block imports fundamental Python libraries for data analysis and visualization**\n", "- `pandas:` For data manipulation and analysis\n", "- `numpy:` For numerical computations\n", "- `matplotlib:` For creating visualizations and plots\n", "\n", "**The version checks help ensure:**\n", "- *Code compatibility across different environments*\n", "- *Reproducibility of analysis*\n", "- *Easy debugging of version-specific issues*\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pandas version: 2.2.3\n", "NumPy version: 2.2.4\n", "matplotlib version: 3.10.1\n"]}], "source": ["# Import data analysis tools\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "print(f\"pandas version: {pd.__version__}\")\n", "print(f\"NumPy version: {np.__version__}\")\n", "print(f\"matplotlib version: {matplotlib.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Import and Validate Preprocessed Data**\n", "\n", "This code block serves two important purposes:\n", "\n", "- Imports a preprocessed CSV file containing bulldozer data using pandas\n", "- Implements error handling to ensure smooth data loading:\n", "    - On success: Displays confirmation message and shows first few rows of data\n", "    - On failure: Prints detailed error message for debugging"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SUCCESSFULLY IMPORTED! The data file 'TrainAndValid_object_values_as_categories.csv' has been successfully imported.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SalesID</th>\n", "      <th>SalePrice</th>\n", "      <th>MachineID</th>\n", "      <th>ModelID</th>\n", "      <th>datasource</th>\n", "      <th>auctioneerID</th>\n", "      <th>YearMade</th>\n", "      <th>MachineHoursCurrentMeter</th>\n", "      <th>UsageBand</th>\n", "      <th>fiModelDesc</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON>_Mounting</th>\n", "      <th>Blade_Type</th>\n", "      <th>Travel_Controls</th>\n", "      <th>Differential_Type</th>\n", "      <th>Steering_Controls</th>\n", "      <th>saleYear</th>\n", "      <th>saleMonth</th>\n", "      <th>saleDay</th>\n", "      <th>saleDayofweek</th>\n", "      <th>saleDayofyear</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1139246</td>\n", "      <td>66000.0</td>\n", "      <td>999089</td>\n", "      <td>3157</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>2004</td>\n", "      <td>68.0</td>\n", "      <td>Low</td>\n", "      <td>521D</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Standard</td>\n", "      <td>Conventional</td>\n", "      <td>2006</td>\n", "      <td>11</td>\n", "      <td>16</td>\n", "      <td>3</td>\n", "      <td>320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1139248</td>\n", "      <td>57000.0</td>\n", "      <td>117657</td>\n", "      <td>77</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>1996</td>\n", "      <td>4640.0</td>\n", "      <td>Low</td>\n", "      <td>950FII</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Standard</td>\n", "      <td>Conventional</td>\n", "      <td>2004</td>\n", "      <td>3</td>\n", "      <td>26</td>\n", "      <td>4</td>\n", "      <td>86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1139249</td>\n", "      <td>10000.0</td>\n", "      <td>434808</td>\n", "      <td>7009</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>2001</td>\n", "      <td>2838.0</td>\n", "      <td>High</td>\n", "      <td>226</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2004</td>\n", "      <td>2</td>\n", "      <td>26</td>\n", "      <td>3</td>\n", "      <td>57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1139251</td>\n", "      <td>38500.0</td>\n", "      <td>1026470</td>\n", "      <td>332</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>2001</td>\n", "      <td>3486.0</td>\n", "      <td>High</td>\n", "      <td>PC120-6E</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2011</td>\n", "      <td>5</td>\n", "      <td>19</td>\n", "      <td>3</td>\n", "      <td>139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1139253</td>\n", "      <td>11000.0</td>\n", "      <td>1057373</td>\n", "      <td>17311</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>2007</td>\n", "      <td>722.0</td>\n", "      <td>Medium</td>\n", "      <td>S175</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2009</td>\n", "      <td>7</td>\n", "      <td>23</td>\n", "      <td>3</td>\n", "      <td>204</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 57 columns</p>\n", "</div>"], "text/plain": ["   SalesID  SalePrice  MachineID  ModelID  datasource  auctioneerID  YearMade  \\\n", "0  1139246    66000.0     999089     3157         121           3.0      2004   \n", "1  1139248    57000.0     117657       77         121           3.0      1996   \n", "2  1139249    10000.0     434808     7009         121           3.0      2001   \n", "3  1139251    38500.0    1026470      332         121           3.0      2001   \n", "4  1139253    11000.0    1057373    17311         121           3.0      2007   \n", "\n", "   MachineHoursCurrentMeter UsageBand fiModelDesc  ... Backhoe_Mounting  \\\n", "0                      68.0       Low        521D  ...              NaN   \n", "1                    4640.0       Low      950FII  ...              NaN   \n", "2                    2838.0      High         226  ...              NaN   \n", "3                    3486.0      High    PC120-6E  ...              NaN   \n", "4                     722.0    Medium        S175  ...              NaN   \n", "\n", "  Blade_Type Travel_Controls Differential_Type Steering_Controls saleYear  \\\n", "0        NaN             NaN          Standard      Conventional     2006   \n", "1        NaN             NaN          Standard      Conventional     2004   \n", "2        NaN             NaN               NaN               NaN     2004   \n", "3        NaN             NaN               NaN               NaN     2011   \n", "4        NaN             NaN               NaN               NaN     2009   \n", "\n", "  saleMonth saleDay saleDayofweek saleDayofyear  \n", "0        11      16             3           320  \n", "1         3      26             4            86  \n", "2         2      26             3            57  \n", "3         5      19             3           139  \n", "4         7      23             3           204  \n", "\n", "[5 rows x 57 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "\n", "# Attempt to import the preprocessed data file\n", "try:\n", "    df_tmp = pd.read_csv(\"C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_object_values_as_categories.csv\",\n", "                         low_memory=False)\n", "    print(\"SUCCESSFULLY IMPORTED! The data file 'TrainAndValid_object_values_as_categories.csv' has been successfully imported.\")\n", "    display(df_tmp.head())\n", "except Exception as e:\n", "    print(f\"An error occurred while importing the preprocessed data: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Display Data Information\n", "\n", "The DataFrame's information provides crucial insights into our data structure and quality. Let's examine the key aspects:\n", "\n", "### Data Overview\n", "\n", "This code generates a comprehensive summary of our DataFrame, displaying:\n", "\n", "- Total number of entries\n", "- Column names and their data types\n", "- Memory usage statistics\n", "- Non-null count per column\n", "\n", "### Data Type Conversion Note\n", "\n", "You'll notice that our previously converted category columns have reverted to object datatypes. This occurs because CSV files store all values as strings, and pandas defaults to object datatypes when reading them. We'll convert these back to category datatypes later."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 412698 entries, 0 to 412697\n", "Data columns (total 57 columns):\n", " #   Column                    Non-Null Count   Dtype  \n", "---  ------                    --------------   -----  \n", " 0   SalesID                   412698 non-null  int64  \n", " 1   SalePrice                 412698 non-null  float64\n", " 2   MachineID                 412698 non-null  int64  \n", " 3   ModelID                   412698 non-null  int64  \n", " 4   datasource                412698 non-null  int64  \n", " 5   auctioneerID              392562 non-null  float64\n", " 6   YearMade                  412698 non-null  int64  \n", " 7   MachineHoursCurrentMeter  147504 non-null  float64\n", " 8   UsageBand                 73670 non-null   object \n", " 9   fiModelDesc               412698 non-null  object \n", " 10  fiBaseModel               412698 non-null  object \n", " 11  fiSecondaryDesc           271971 non-null  object \n", " 12  fiModelSeries             58667 non-null   object \n", " 13  fiModelDescriptor         74816 non-null   object \n", " 14  ProductSize               196093 non-null  object \n", " 15  fiProductClassDesc        412698 non-null  object \n", " 16  state                     412698 non-null  object \n", " 17  ProductGroup              412698 non-null  object \n", " 18  ProductGroupDesc          412698 non-null  object \n", " 19  Drive_System              107087 non-null  object \n", " 20  Enclosure                 412364 non-null  object \n", " 21  Forks                     197715 non-null  object \n", " 22  Pad_Type                  81096 non-null   object \n", " 23  Ride_Control              152728 non-null  object \n", " 24  Stick                     81096 non-null   object \n", " 25  Transmission              188007 non-null  object \n", " 26  Turbocharged              81096 non-null   object \n", " 27  Blade_Extension           25983 non-null   object \n", " 28  <PERSON>_Width               25983 non-null   object \n", " 29  Enclosure_Type            25983 non-null   object \n", " 30  Engine_Horsepower         25983 non-null   object \n", " 31  Hydraulics                330133 non-null  object \n", " 32  Pushblock                 25983 non-null   object \n", " 33  <PERSON><PERSON><PERSON>                    106945 non-null  object \n", " 34  Scarifier                 25994 non-null   object \n", " 35  Tip_Control               25983 non-null   object \n", " 36  Tire_Size                 97638 non-null   object \n", " 37  <PERSON>upler                   220679 non-null  object \n", " 38  Coupler_System            44974 non-null   object \n", " 39  Grouser_Tracks            44875 non-null   object \n", " 40  Hydraulics_Flow           44875 non-null   object \n", " 41  Track_Type                102193 non-null  object \n", " 42  Undercarriage_Pad_Width   102916 non-null  object \n", " 43  Stick_Length              102261 non-null  object \n", " 44  Thumb                     102332 non-null  object \n", " 45  Pattern_Changer           102261 non-null  object \n", " 46  Grouser_Type              102193 non-null  object \n", " 47  Backhoe_Mounting          80712 non-null   object \n", " 48  Blade_Type                81875 non-null   object \n", " 49  Travel_Controls           81877 non-null   object \n", " 50  Differential_Type         71564 non-null   object \n", " 51  Steering_Controls         71522 non-null   object \n", " 52  saleYear                  412698 non-null  int64  \n", " 53  saleMonth                 412698 non-null  int64  \n", " 54  saleDay                   412698 non-null  int64  \n", " 55  saleDayofweek             412698 non-null  int64  \n", " 56  saleDayofyear             412698 non-null  int64  \n", "dtypes: float64(3), int64(10), object(44)\n", "memory usage: 179.5+ MB\n"]}], "source": ["df_tmp.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Convert Object Columns to Category Datatypes\n", "\n", "This code changes how data is stored to save computer memory. It takes text data (stored as \"objects\") and converts it into a more efficient format called \"categories\":\n", "\n", "- Iterates through each column in the DataFrame\n", "- Checks if the column has object (string) datatype\n", "- Converts qualifying columns to category datatype to reduce memory usage"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["for label, content in df_tmp.items():\n", "    if pd.api.types.is_object_dtype(content):\n", "        # Turn object columns into category datatype\n", "        df_tmp[label] = df_tmp[label].astype(\"category\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Preprocessed Data to Parquet Format\n", "\n", "This code changes our data file from CSV format to Parquet format to make it work better:\n", "\n", "- Faster read/write operations\n", "- Better compression for storage efficiency\n", "- Column-oriented storage for optimized querying"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SUCCESSFULLY SAVED! The TrainAndValid_object_values_as_categories.parquet is successfully saved in the data/processed.\n"]}], "source": ["# To save to parquet format requires pyarrow or fastparquet (or both)\n", "# Can install via `pip install pyarrow fastparquet`\n", "import pandas as pd\n", "\n", "# Assuming df_tmp is your DataFrame\n", "df_tmp.to_parquet(path=\"C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_object_values_as_categories.parquet\", \n", "                  engine=\"auto\") # \"auto\" will automatically use pyarrow or fastparquet, defaulting to pyarrow first\n", "\n", "print(\"SUCCESSFULLY SAVED! The TrainAndValid_object_values_as_categories.parquet is successfully saved in the data/processed.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import and Verify Parquet Data\n", "\n", "This code block performs two essential functions for our data pipeline:\n", "\n", "- Reads the preprocessed bulldozer dataset from a Parquet file, which is more efficient than CSV format for large datasets\n", "- Verifies the data structure by displaying DataFrame information, ensuring all datatypes are preserved correctly from our previous preprocessing ste"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 412698 entries, 0 to 412697\n", "Data columns (total 57 columns):\n", " #   Column                    Non-Null Count   Dtype   \n", "---  ------                    --------------   -----   \n", " 0   SalesID                   412698 non-null  int64   \n", " 1   SalePrice                 412698 non-null  float64 \n", " 2   MachineID                 412698 non-null  int64   \n", " 3   ModelID                   412698 non-null  int64   \n", " 4   datasource                412698 non-null  int64   \n", " 5   auctioneerID              392562 non-null  float64 \n", " 6   YearMade                  412698 non-null  int64   \n", " 7   MachineHoursCurrentMeter  147504 non-null  float64 \n", " 8   UsageBand                 73670 non-null   category\n", " 9   fiModelDesc               412698 non-null  category\n", " 10  fiBaseModel               412698 non-null  category\n", " 11  fiSecondaryDesc           271971 non-null  category\n", " 12  fiModelSeries             58667 non-null   category\n", " 13  fiModelDescriptor         74816 non-null   category\n", " 14  ProductSize               196093 non-null  category\n", " 15  fiProductClassDesc        412698 non-null  category\n", " 16  state                     412698 non-null  category\n", " 17  ProductGroup              412698 non-null  category\n", " 18  ProductGroupDesc          412698 non-null  category\n", " 19  Drive_System              107087 non-null  category\n", " 20  Enclosure                 412364 non-null  category\n", " 21  Forks                     197715 non-null  category\n", " 22  Pad_Type                  81096 non-null   category\n", " 23  Ride_Control              152728 non-null  category\n", " 24  Stick                     81096 non-null   category\n", " 25  Transmission              188007 non-null  category\n", " 26  Turbocharged              81096 non-null   category\n", " 27  Blade_Extension           25983 non-null   category\n", " 28  <PERSON><PERSON>Width               25983 non-null   category\n", " 29  Enclosure_Type            25983 non-null   category\n", " 30  Engine_Horsepower         25983 non-null   category\n", " 31  Hydraulics                330133 non-null  category\n", " 32  Pushblock                 25983 non-null   category\n", " 33  <PERSON><PERSON><PERSON>                    106945 non-null  category\n", " 34  Scarifier                 25994 non-null   category\n", " 35  Tip_Control               25983 non-null   category\n", " 36  Tire_Size                 97638 non-null   category\n", " 37  <PERSON><PERSON><PERSON>                   220679 non-null  category\n", " 38  Coupler_System            44974 non-null   category\n", " 39  Grouser_Tracks            44875 non-null   category\n", " 40  Hydraulics_Flow           44875 non-null   category\n", " 41  Track_Type                102193 non-null  category\n", " 42  Undercarriage_Pad_Width   102916 non-null  category\n", " 43  Stick_Length              102261 non-null  category\n", " 44  Thumb                     102332 non-null  category\n", " 45  Pattern_Changer           102261 non-null  category\n", " 46  Grouser_Type              102193 non-null  category\n", " 47  Back<PERSON>_Mounting          80712 non-null   category\n", " 48  Blade_Type                81875 non-null   category\n", " 49  Travel_Controls           81877 non-null   category\n", " 50  Differential_Type         71564 non-null   category\n", " 51  Steering_Controls         71522 non-null   category\n", " 52  saleYear                  412698 non-null  int64   \n", " 53  saleMonth                 412698 non-null  int64   \n", " 54  saleDay                   412698 non-null  int64   \n", " 55  saleDayofweek             412698 non-null  int64   \n", " 56  saleDayofyear             412698 non-null  int64   \n", "dtypes: category(44), float64(3), int64(10)\n", "memory usage: 60.5 MB\n"]}], "source": ["# Read in df_tmp from parquet format\n", "df_tmp = pd.read_parquet(path=\"C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_object_values_as_categories.parquet\",\n", "                        engine=\"auto\")\n", "\n", "# Using parquet format, datatypes are preserved\n", "df_tmp.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Finding and filling missing values**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check Missing Values in Dataset\n", "\n", "This code analyzes and displays missing values in our bulldozer dataset:\n", "\n", "- Uses pandas' isna() function to identify missing values\n", "- Counts total missing values per column using sum()\n", "- Sorts results in descending order to highlight columns with most missing data\n", "- Displays top 25 columns with highest number of missing values"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["Blade_Width          386715\n", "Enclosure_Type       386715\n", "Engine_Horsepower    386715\n", "Tip_Control          386715\n", "Pushblock            386715\n", "Blade_Extension      386715\n", "Scarifier            386704\n", "Grouser_Tracks       367823\n", "Hydraulics_Flow      367823\n", "Coupler_System       367724\n", "fiModelSeries        354031\n", "Steering_Controls    341176\n", "Differential_Type    341134\n", "UsageBand            339028\n", "fiModelDescriptor    337882\n", "Backhoe_Mounting     331986\n", "Turbocharged         331602\n", "Stick                331602\n", "Pad_Type             331602\n", "Blade_Type           330823\n", "Travel_Controls      330821\n", "Tire_Size            315060\n", "Track_Type           310505\n", "Grouser_Type         310505\n", "Stick_Length         310437\n", "dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check missing values\n", "df_tmp.isna().sum().sort_values(ascending=False)[:25]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Filling missing numerical values**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Identify and Analyze Numeric Columns\n", "\n", "This code block examines the numeric columns in our DataFrame to understand their data types and values:\n", "\n", "- Iterates through each column in the DataFrame to find numeric data types\n", "- For each numeric column, it:\n", "- Checks and displays the column's data type\n", "- Takes a random sample value from the column\n", "- Infers and shows the data type of the sample value"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column name: SalesID | Column dtype: int64 | Example value: [2228692] | Example value dtype: integer\n", "Column name: SalePrice | Column dtype: float64 | Example value: [17000.] | Example value dtype: floating\n", "Column name: Machine<PERSON> | Column dtype: int64 | Example value: [1066403] | Example value dtype: integer\n", "Column name: ModelID | Column dtype: int64 | Example value: [1092] | Example value dtype: integer\n", "Column name: datasource | Column dtype: int64 | Example value: [132] | Example value dtype: integer\n", "Column name: auctioneerID | Column dtype: float64 | Example value: [1.] | Example value dtype: floating\n", "Column name: YearMade | Column dtype: int64 | Example value: [1000] | Example value dtype: integer\n", "Column name: MachineHoursCurrentMeter | Column dtype: float64 | Example value: [12067.] | Example value dtype: floating\n", "Column name: sale<PERSON>ear | Column dtype: int64 | Example value: [2010] | Example value dtype: integer\n", "Column name: <PERSON><PERSON><PERSON><PERSON> | Column dtype: int64 | Example value: [10] | Example value dtype: integer\n", "Column name: saleDay | Column dtype: int64 | Example value: [25] | Example value dtype: integer\n", "Column name: saleDayofweek | Column dtype: int64 | Example value: [1] | Example value dtype: integer\n", "Column name: saleDayofyear | Column dtype: int64 | Example value: [334] | Example value dtype: integer\n"]}], "source": ["# Find numeric columns \n", "for label, content in df_tmp.items():\n", "    if pd.api.types.is_numeric_dtype(content):\n", "        # Check datatype of target column\n", "        column_datatype = df_tmp[label].dtype.name\n", "\n", "        # Get random sample from column values\n", "        example_value = content.sample(1).values\n", "\n", "        # Infer random sample datatype\n", "        example_value_dtype = pd.api.types.infer_dtype(example_value)\n", "        print(f\"Column name: {label} | Column dtype: {column_datatype} | Example value: {example_value} | Example value dtype: {example_value_dtype}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check for Missing Values in Numeric Columns\n", "\n", "This code block identifies which numeric columns in our DataFrame contain missing (null) values. It's important for data preprocessing because:\n", "\n", "- Helps identify potential data quality issues\n", "- Guides our strategy for handling missing values\n", "- Essential for ensuring model reliability"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column name: SalesID | Has missing values: False\n", "Column name: SalePrice | Has missing values: False\n", "Column name: <PERSON><PERSON> | Has missing values: False\n", "Column name: <PERSON><PERSON> | Has missing values: False\n", "Column name: datasource | Has missing values: False\n", "Column name: auctioneerID | Has missing values: True\n", "Column name: <PERSON><PERSON><PERSON> | Has missing values: False\n", "Column name: MachineHoursCurrentMeter | Has missing values: True\n", "Column name: sale<PERSON>ear | Has missing values: False\n", "Column name: <PERSON><PERSON><PERSON><PERSON> | Has missing values: False\n", "Column name: saleDay | Has missing values: False\n", "Column name: saleDayofweek | Has missing values: False\n", "Column name: <PERSON><PERSON><PERSON><PERSON><PERSON>ar | Has missing values: False\n"]}], "source": ["# Check for which numeric columns have null values\n", "for label, content in df_tmp.items():\n", "    if pd.api.types.is_numeric_dtype(content):\n", "        if pd.isnull(content).sum():\n", "            print(f\"Column name: {label} | Has missing values: {True}\")\n", "        else:\n", "            print(f\"Column name: {label} | Has missing values: {False}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fill Missing Numeric Values with Median\n", "\n", "This code block handles missing values in numeric columns using a two-step approach:\n", "\n", "- Creates indicator columns to track which values were originally missing\n", "- Replaces missing values with column medians for better statistical robustness"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Fill missing numeric values with the median of the target column\n", "for label, content in df_tmp.items():\n", "    if pd.api.types.is_numeric_dtype(content):\n", "        if pd.isnull(content).sum():\n", "            \n", "            # Add a binary column which tells if the data was missing our not\n", "            df_tmp[label+\"_is_missing\"] = pd.isnull(content).astype(int) # this will add a 0 or 1 value to rows with missing values (e.g. 0 = not missing, 1 = missing)\n", "\n", "            # Fill missing numeric values with median since it's more robust than the mean\n", "            df_tmp[label] = content.fillna(content.median())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display Missing Value Examples\n", "\n", "This code helps us find rows in our data where values were missing and have been filled in:\n", "\n", "- Shows 5 random sample rows where `MachineHoursCurrentMeter` values were originally missing\n", "- Helps verify our missing value handling strategy"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SalesID</th>\n", "      <th>SalePrice</th>\n", "      <th>MachineID</th>\n", "      <th>ModelID</th>\n", "      <th>datasource</th>\n", "      <th>auctioneerID</th>\n", "      <th>YearMade</th>\n", "      <th>MachineHoursCurrentMeter</th>\n", "      <th>UsageBand</th>\n", "      <th>fiModelDesc</th>\n", "      <th>...</th>\n", "      <th>Travel_Controls</th>\n", "      <th>Differential_Type</th>\n", "      <th>Steering_Controls</th>\n", "      <th>saleYear</th>\n", "      <th>saleMonth</th>\n", "      <th>saleDay</th>\n", "      <th>saleDayofweek</th>\n", "      <th>saleDayofyear</th>\n", "      <th>auctioneerID_is_missing</th>\n", "      <th>MachineHoursCurrentMeter_is_missing</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>407337</th>\n", "      <td>6267755</td>\n", "      <td>45000.0</td>\n", "      <td>1076488</td>\n", "      <td>3362</td>\n", "      <td>149</td>\n", "      <td>1.0</td>\n", "      <td>1981</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>140G</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2012</td>\n", "      <td>3</td>\n", "      <td>14</td>\n", "      <td>2</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64803</th>\n", "      <td>1334421</td>\n", "      <td>85000.0</td>\n", "      <td>373084</td>\n", "      <td>1211</td>\n", "      <td>132</td>\n", "      <td>19.0</td>\n", "      <td>1997</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>325BL</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2001</td>\n", "      <td>11</td>\n", "      <td>8</td>\n", "      <td>3</td>\n", "      <td>312</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>273642</th>\n", "      <td>1816536</td>\n", "      <td>25000.0</td>\n", "      <td>1151373</td>\n", "      <td>9593</td>\n", "      <td>132</td>\n", "      <td>16.0</td>\n", "      <td>1986</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>L160</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>Standard</td>\n", "      <td>Conventional</td>\n", "      <td>2010</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>126</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72191</th>\n", "      <td>1355358</td>\n", "      <td>80000.0</td>\n", "      <td>598509</td>\n", "      <td>1355</td>\n", "      <td>132</td>\n", "      <td>1.0</td>\n", "      <td>1997</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>350L</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2004</td>\n", "      <td>11</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>321</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176693</th>\n", "      <td>1607666</td>\n", "      <td>32000.0</td>\n", "      <td>1440022</td>\n", "      <td>4753</td>\n", "      <td>132</td>\n", "      <td>1.0</td>\n", "      <td>1994</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>644G</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>Standard</td>\n", "      <td>Conventional</td>\n", "      <td>2002</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>337</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 59 columns</p>\n", "</div>"], "text/plain": ["        SalesID  SalePrice  MachineID  ModelID  datasource  auctioneerID  \\\n", "407337  6267755    45000.0    1076488     3362         149           1.0   \n", "64803   1334421    85000.0     373084     1211         132          19.0   \n", "273642  1816536    25000.0    1151373     9593         132          16.0   \n", "72191   1355358    80000.0     598509     1355         132           1.0   \n", "176693  1607666    32000.0    1440022     4753         132           1.0   \n", "\n", "        YearMade  MachineHoursCurrentMeter UsageBand fiModelDesc  ...  \\\n", "407337      1981                       0.0       NaN        140G  ...   \n", "64803       1997                       0.0       NaN       325BL  ...   \n", "273642      1986                       0.0       NaN        L160  ...   \n", "72191       1997                       0.0       NaN        350L  ...   \n", "176693      1994                       0.0       NaN        644G  ...   \n", "\n", "       Travel_Controls Differential_Type Steering_Controls saleYear saleMonth  \\\n", "407337             NaN               NaN               NaN     2012         3   \n", "64803              NaN               NaN               NaN     2001        11   \n", "273642             NaN          Standard      Conventional     2010         5   \n", "72191              NaN               NaN               NaN     2004        11   \n", "176693             NaN          Standard      Conventional     2002        12   \n", "\n", "       saleDay saleDayofweek saleDayofyear auctioneerID_is_missing  \\\n", "407337      14             2            74                       0   \n", "64803        8             3           312                       0   \n", "273642       6             3           126                       0   \n", "72191       16             1           321                       0   \n", "176693       3             1           337                       0   \n", "\n", "       MachineHoursCurrentMeter_is_missing  \n", "407337                                   1  \n", "64803                                    1  \n", "273642                                   1  \n", "72191                                    1  \n", "176693                                   1  \n", "\n", "[5 rows x 59 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Show rows where MachineHoursCurrentMeter_is_missing == 1\n", "df_tmp[df_tmp[\"MachineHoursCurrentMeter_is_missing\"] == 1].sample(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check for Missing Values in Numeric Columns\n", "This code iterates through each column in the DataFrame to identify numeric columns with missing values.\n", "Why: Understanding which numeric columns have missing values is crucial for:\n", " - Data quality assessment\n", " - Determining appropriate imputation strategies\n", " - Ensuring model reliability\n", " Code implementation:\n", "\n", " Check for which numeric columns have null values"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column name: SalesID | Has missing values: False\n", "Column name: SalePrice | Has missing values: False\n", "Column name: <PERSON><PERSON> | Has missing values: False\n", "Column name: <PERSON><PERSON> | Has missing values: False\n", "Column name: datasource | Has missing values: False\n", "Column name: auctioneerID | Has missing values: False\n", "Column name: <PERSON><PERSON><PERSON> | Has missing values: False\n", "Column name: MachineHoursCurrentMeter | Has missing values: False\n", "Column name: sale<PERSON>ear | Has missing values: False\n", "Column name: <PERSON><PERSON><PERSON><PERSON> | Has missing values: False\n", "Column name: saleDay | Has missing values: False\n", "Column name: saleDayofweek | Has missing values: False\n", "Column name: <PERSON><PERSON><PERSON><PERSON><PERSON>ar | Has missing values: False\n", "Column name: auctioneerID_is_missing | Has missing values: False\n", "Column name: MachineHoursCurrentMeter_is_missing | Has missing values: False\n"]}], "source": ["# Check for which numeric columns have null values\n", "for label, content in df_tmp.items():\n", "    if pd.api.types.is_numeric_dtype(content):\n", "        if pd.isnull(content).sum():\n", "            print(f\"Column name: {label} | Has missing values: {True}\")\n", "        else:\n", "            print(f\"Column name: {label} | Has missing values: {False}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Identify Non-Numeric Columns\n", "\n", "This code block finds and shows which columns in our data contain text or other non-number information:\n", "\n", "- Helps understand which columns need special handling for categorical data\n", "- Shows data type information for non-numeric columns\n", "- Important for preprocessing decisions like encoding categorical variables"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Columns which are not numeric:\n", "Column name: UsageBand | Column dtype: category\n", "Column name: fiModelDesc | Column dtype: category\n", "Column name: fiBaseModel | Column dtype: category\n", "Column name: fiSecondaryDesc | Column dtype: category\n", "Column name: fiModelSeries | Column dtype: category\n", "Column name: fiModelDescriptor | Column dtype: category\n", "Column name: ProductSize | Column dtype: category\n", "Column name: fiProductClassDesc | Column dtype: category\n", "Column name: state | Column dtype: category\n", "Column name: ProductGroup | Column dtype: category\n", "Column name: ProductGroupDesc | Column dtype: category\n", "Column name: Drive_System | Column dtype: category\n", "Column name: Enclosure | Column dtype: category\n", "Column name: Forks | Column dtype: category\n", "Column name: Pad_Type | Column dtype: category\n", "Column name: Ride_Control | Column dtype: category\n", "Column name: <PERSON> | Column dtype: category\n", "Column name: Transmission | Column dtype: category\n", "Column name: Turbocharged | Column dtype: category\n", "Column name: Blade_Extension | Column dtype: category\n", "Column name: <PERSON>_Width | Column dtype: category\n", "Column name: Enclosure_Type | Column dtype: category\n", "Column name: Engine_Horsepower | Column dtype: category\n", "Column name: Hydraulics | Column dtype: category\n", "Column name: <PERSON><PERSON><PERSON> | Column dtype: category\n", "Column name: <PERSON><PERSON><PERSON> | Column dtype: category\n", "Column name: Scarifier | Column dtype: category\n", "Column name: Tip_Control | Column dtype: category\n", "Column name: <PERSON><PERSON>_<PERSON><PERSON> | Column dtype: category\n", "Column name: <PERSON><PERSON><PERSON> | Column dtype: category\n", "Column name: Coupler_System | Column dtype: category\n", "Column name: Grouser_Tracks | Column dtype: category\n", "Column name: Hydraulics_Flow | Column dtype: category\n", "Column name: Track_Type | Column dtype: category\n", "Column name: Undercarriage_Pad_Width | Column dtype: category\n", "Column name: Stick_<PERSON> | Column dtype: category\n", "Column name: Thumb | Column dtype: category\n", "Column name: Pattern_Changer | Column dtype: category\n", "Column name: Grouser_Type | Column dtype: category\n", "Column name: <PERSON><PERSON>_Mounting | Column dtype: category\n", "Column name: Blade_Type | Column dtype: category\n", "Column name: Travel_Controls | Column dtype: category\n", "Column name: Differential_Type | Column dtype: category\n", "Column name: Steering_Controls | Column dtype: category\n"]}], "source": ["# Check columns which aren't numeric\n", "print(f\"[INFO] Columns which are not numeric:\")\n", "for label, content in df_tmp.items():\n", "    if not pd.api.types.is_numeric_dtype(content):\n", "        print(f\"Column name: {label} | Column dtype: {df_tmp[label].dtype.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Convert Categorical Variables to Numeric Values\n", "\n", "This code changes text-based data (like categories) into numbers that a computer can work with better. It does this while keeping track of what the original text values were:\n", "\n", "- Creates a dictionary to store the mapping between numeric codes and original categories\n", "- Processes each non-numeric column by:\n", "    - Adding a binary indicator for missing values\n", "    - Converting categories to numeric codes starting from 1\n", "    - Storing the mapping for future reference"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# 1. Create a dictionary to store column to category values (e.g. we turn our category types into numbers but we keep a record so we can go back)\n", "column_to_category_dict = {} \n", "\n", "# 2. Turn categorical variables into numbers\n", "for label, content in df_tmp.items():\n", "\n", "    # 3. Check columns which *aren't* numeric\n", "    if not pd.api.types.is_numeric_dtype(content):\n", "\n", "        # 4. Add binary column to inidicate whether sample had missing value\n", "        df_tmp[label+\"_is_missing\"] = pd.isnull(content).astype(int)\n", "\n", "        # 5. Ensure content is categorical and get its category codes\n", "        content_categories = pd.Categorical(content)\n", "        content_category_codes = content_categories.codes + 1 # prevents -1 (the default for NaN values) from being used for missing values (we'll treat missing values as 0)\n", "\n", "        # 6. Add column key to dictionary with code: category mapping per column\n", "        column_to_category_dict[label] = dict(zip(content_category_codes, content_categories))\n", "        \n", "        # 7. Set the column to the numerical values (the category code value) \n", "        df_tmp[label] = content_category_codes      "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display Random Sample Rows\n", "\n", "This code displays 5 randomly selected rows from our DataFrame to:\n", "\n", "- Quickly inspect the data structure and content\n", "- Verify data preprocessing steps were successful\n", "- Help identify potential patterns or anomalies in the data"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SalesID</th>\n", "      <th>SalePrice</th>\n", "      <th>MachineID</th>\n", "      <th>ModelID</th>\n", "      <th>datasource</th>\n", "      <th>auctioneerID</th>\n", "      <th>YearMade</th>\n", "      <th>MachineHoursCurrentMeter</th>\n", "      <th>UsageBand</th>\n", "      <th>fiModelDesc</th>\n", "      <th>...</th>\n", "      <th>Undercarriage_Pad_Width_is_missing</th>\n", "      <th>Stick_Length_is_missing</th>\n", "      <th>Thumb_is_missing</th>\n", "      <th>Pattern_Changer_is_missing</th>\n", "      <th>Grouser_Type_is_missing</th>\n", "      <th><PERSON><PERSON>_Mounting_is_missing</th>\n", "      <th>Blade_Type_is_missing</th>\n", "      <th>Travel_Controls_is_missing</th>\n", "      <th>Differential_Type_is_missing</th>\n", "      <th>Steering_Controls_is_missing</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>394320</th>\n", "      <td>6267829</td>\n", "      <td>52000.0</td>\n", "      <td>1930309</td>\n", "      <td>3362</td>\n", "      <td>149</td>\n", "      <td>1.0</td>\n", "      <td>1987</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>107</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23101</th>\n", "      <td>1219664</td>\n", "      <td>15000.0</td>\n", "      <td>176537</td>\n", "      <td>2752</td>\n", "      <td>121</td>\n", "      <td>3.0</td>\n", "      <td>2000</td>\n", "      <td>5782.0</td>\n", "      <td>3</td>\n", "      <td>2163</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31229</th>\n", "      <td>1263365</td>\n", "      <td>14000.0</td>\n", "      <td>1535547</td>\n", "      <td>3323</td>\n", "      <td>132</td>\n", "      <td>22.0</td>\n", "      <td>1980</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>4746</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>358514</th>\n", "      <td>2454762</td>\n", "      <td>54000.0</td>\n", "      <td>1782515</td>\n", "      <td>18180</td>\n", "      <td>136</td>\n", "      <td>99.0</td>\n", "      <td>2004</td>\n", "      <td>7886.0</td>\n", "      <td>1</td>\n", "      <td>2555</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148766</th>\n", "      <td>1521980</td>\n", "      <td>36000.0</td>\n", "      <td>1484867</td>\n", "      <td>4199</td>\n", "      <td>132</td>\n", "      <td>2.0</td>\n", "      <td>1989</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2460</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 103 columns</p>\n", "</div>"], "text/plain": ["        SalesID  SalePrice  MachineID  ModelID  datasource  auctioneerID  \\\n", "394320  6267829    52000.0    1930309     3362         149           1.0   \n", "23101   1219664    15000.0     176537     2752         121           3.0   \n", "31229   1263365    14000.0    1535547     3323         132          22.0   \n", "358514  2454762    54000.0    1782515    18180         136          99.0   \n", "148766  1521980    36000.0    1484867     4199         132           2.0   \n", "\n", "        YearMade  MachineHoursCurrentMeter  UsageBand  fiModelDesc  ...  \\\n", "394320      1987                       0.0          0          107  ...   \n", "23101       2000                    5782.0          3         2163  ...   \n", "31229       1980                       0.0          0         4746  ...   \n", "358514      2004                    7886.0          1         2555  ...   \n", "148766      1989                       0.0          0         2460  ...   \n", "\n", "        Undercarriage_Pad_Width_is_missing  Stick_Length_is_missing  \\\n", "394320                                   1                        1   \n", "23101                                    1                        1   \n", "31229                                    1                        1   \n", "358514                                   0                        0   \n", "148766                                   0                        0   \n", "\n", "        Thumb_is_missing  Pattern_Changer_is_missing  Grouser_Type_is_missing  \\\n", "394320                 1                           1                        1   \n", "23101                  1                           1                        1   \n", "31229                  1                           1                        1   \n", "358514                 0                           0                        0   \n", "148766                 0                           0                        0   \n", "\n", "        Backhoe_Mounting_is_missing  Blade_Type_is_missing  \\\n", "394320                            1                      1   \n", "23101                             0                      0   \n", "31229                             1                      1   \n", "358514                            1                      1   \n", "148766                            1                      1   \n", "\n", "        Travel_Controls_is_missing  Differential_Type_is_missing  \\\n", "394320                           1                             1   \n", "23101                            0                             1   \n", "31229                            1                             0   \n", "358514                           1                             1   \n", "148766                           1                             1   \n", "\n", "        Steering_Controls_is_missing  \n", "394320                             1  \n", "23101                              1  \n", "31229                              0  \n", "358514                             1  \n", "148766                             1  \n", "\n", "[5 rows x 103 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df_tmp.sample(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display UsageBand Categories and Their Numeric Mappings\n", "This code shows how we convert text-based usage levels (like 'Low', 'Medium', 'High') into numbers that our computer program can work with. This helps us:\n", "\n", "1. Check that our conversion from text to numbers worked correctly\n", "2. See how each usage category matches up with its new number valu"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 -> nan\n", "1 -> High\n", "2 -> Low\n", "3 -> Medium\n"]}], "source": ["# Check the UsageBand (measure of bulldozer usage)\n", "for key, value in sorted(column_to_category_dict[\"UsageBand\"].items()): # note: calling sorted() on dictionary.items() sorts the dictionary by keys \n", "    print(f\"{key} -> {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display State Category Mappings\n", "\n", "This code shows how we convert state names into numbers in our dataset. This helps our computer understand and work with the data better.\n", "\n", "- Shows the first 10 state categories and their corresponding numeric codes\n", "- Helps verify our categorical encoding is working correctly\n", "- Useful for debugging and data validation"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 -> Alabama\n", "2 -> Alaska\n", "3 -> Arizona\n", "4 -> Arkansas\n", "5 -> California\n", "6 -> Colorado\n", "7 -> Connecticut\n", "8 -> Delaware\n", "9 -> Florida\n", "10 -> Georgia\n"]}], "source": ["# Check the first 10 state column values\n", "for key, value in sorted(column_to_category_dict[\"state\"].items())[:10]:\n", "    print(f\"{key} -> {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verify Missing Values\n", "\n", "This code block performs a final check for any remaining missing values in the dataset:\n", "\n", "- Calculates the total count of missing values across all columns\n", "- Provides user-friendly feedback:\n", "    - Success message if no missing values are found\n", "    - Warning message if missing values still exist"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Total missing values: 0 - Woohoo! Let's build a model!\n"]}], "source": ["# Check total number of missing values\n", "total_missing_values = df_tmp.isna().sum().sum()\n", "\n", "if total_missing_values == 0:\n", "    print(f\"[INFO] Total missing values: {total_missing_values} - Woohoo! Let's build a model!\")\n", "else:\n", "    print(f\"[INFO] Uh ohh... total missing values: {total_missing_values} - Perhaps we might have to retrace our steps to fill the values?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Saving  Preprocessed Data (part 2)**"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Success: The file 'TrainAndValid_object_values_as_categories_and_missing_values_filled.parquet' is saved in the specified directory.\n"]}], "source": ["import os\n", "\n", "# Save preprocessed data with object values as categories as well as missing values filled\n", "df_tmp.to_parquet(path=\"C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_object_values_as_categories_and_missing_values_filled.parquet\",\n", "                engine=\"auto\")\n", "\n", "# Define the file path\n", "file_path = \"C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_object_values_as_categories_and_missing_values_filled.parquet\"\n", "\n", "# Check if the file exists\n", "if os.path.isfile(file_path):\n", "    print(\"Success: The file 'TrainAndValid_object_values_as_categories_and_missing_values_filled.parquet' is saved in the specified directory.\")\n", "else:\n", "    print(\"Error: The file 'TrainAndValid_object_values_as_categories_and_missing_values_filled.parquet' does not exist in the specified directory.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Correlation Analysis** \n", "\n", "This correlation analysis reveals how various bulldozer features influence their prices—essentially showing us which characteristics make a bulldozer more or less valuable.\n", "\n", "### How Does It Work?\n", "\n", "- First, we load our bulldozer data from a special file\n", "- Then, we calculate how strongly each feature connects to the price\n", "- Finally, we show these connections from strongest to weakest"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Correlation values with SalePrice:\n", "SalePrice                        1.000000\n", "Coupler_System_is_missing        0.311789\n", "Grouser_Tracks_is_missing        0.311479\n", "Hydraulics_Flow_is_missing       0.311479\n", "fiModelDescriptor                0.295725\n", "                                   ...   \n", "fiBaseModel_is_missing                NaN\n", "fiProductClassDesc_is_missing         NaN\n", "state_is_missing                      NaN\n", "ProductGroup_is_missing               NaN\n", "ProductGroupDesc_is_missing           NaN\n", "Name: <PERSON><PERSON><PERSON>, Length: 103, dtype: float64\n"]}], "source": ["# =================================================================\n", "# BULL<PERSON><PERSON><PERSON>ER PRICE CORRELATION ANALYSIS\n", "# Purpose: Analyze relationships between features and sale price\n", "# =================================================================\n", "\n", "import pandas as pd\n", "\n", "# -----------------------------------------------------------------\n", "# Load and Prepare Data\n", "# -----------------------------------------------------------------\n", "# Import preprocessed parquet file containing bulldozer data\n", "file_path = \"C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_object_values_as_categories_and_missing_values_filled.parquet\"\n", "df = pd.read_parquet(file_path)\n", "\n", "# -----------------------------------------------------------------\n", "# Calculate Correlations\n", "# -----------------------------------------------------------------\n", "# Generate correlation matrix for all numeric features\n", "correlation_matrix = df.corr()\n", "\n", "# Extract and sort correlations with SalePrice\n", "saleprice_correlation = correlation_matrix[\"SalePrice\"].sort_values(ascending=False)\n", "\n", "# -----------------------------------------------------------------\n", "# Output Results\n", "# -----------------------------------------------------------------\n", "# Display sorted correlation values\n", "print(\"Correlation values with SalePrice:\")\n", "print(saleprice_correlation)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Understanding Correlation Analysis\n", "\n", "Let's analyze how different bulldozer characteristics influence their prices:\n", "\n", "### Numeric Feature Correlations\n", "\n", "Several numerical features show moderate positive correlations with sale price:\n", "\n", "- Coupler System (0.31): Equipment attachment mechanisms\n", "- Grouser Tracks (0.31): Track type and specifications\n", "- Hydraulics Flow (0.31): Hydraulic system capacity\n", "- Model Descriptor (0.30): Specific model identifiers\n", "\n", "### Important Note\n", "\n", "Categorical features like state, ProductGroup, and ProductGroupDesc cannot be directly correlated with price since they are text-based. These require special encoding methods to analyze their relationship with price.\n", "\n", "### What This Tells Us\n", "\n", "The analysis reveals that mechanical and structural features have the strongest influence on price. This makes sense as these components directly affect the bulldozer's capabilities and overall value.\n", "\n", "Understanding these relationships helps us build more accurate price prediction models by focusing on the most influential features."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Conclusions and Next Steps**\n", "\n", "## Conclusion\n", "\n", "#### Key Objectives Achieved and Their Importance\n", "- **Data Cleaning and Preparation**: Successfully handled missing values, outliers, and inconsistencies in the dataset, ensuring the data is ready for analysis and modeling.\n", "- **Feature Engineering**: Created new features and transformed existing ones to enhance the predictive power of the dataset.\n", "- **Data Normalization and Scaling**: Made the data consistent by adjusting its values to a standard scale. This step is important because it helps machine learning models work better and make more accurate predictions..\n", "\n", "#### Summary of Traditional Data Analysis Techniques Used\n", "- **Descriptive Statistics**: Calculated mean, median, mode, standard deviation, and other statistical measures to understand the distribution and central tendencies of the data.\n", "- **Data Visualization**: Used histograms, box plots, scatter plots, and correlation matrices to visualize data. distributions, relationships, and identify potential patterns.\n", "- **Correlation Analysis**: Studied how different features relate to each other to find which ones are closely connected and overlapping.\n", "\n", "#### Summary of Machine Learning Techniques Applied\n", "- **Feature Selection**: Used two methods to pick out the most important features for our model: RFE (which removes less useful features one by one) and PCA (which combines features to find the most important patterns in the data)..\n", "- **Model Training and Evaluation**: Trained various machine learning models, including linear regression, decision trees, and random forests, and evaluated their performance using metrics like RMSE, MAE, and R².\n", "- **Hyperparameter Tuning**: Used grid search and cross-validation to optimize the hyperparameters of the selected models, improving their accuracy and robustness.\n", "\n", "#### Main Findings and Results\n", "- **Data Quality Improvement**:\n", "  - Successfully imputed missing values using mean/mode imputation and advanced techniques like KNN imputation.\n", "  - Detected and handled outliers using IQR and Z-score methods, improving data quality.\n", "\n", "- **Feature Engineering**:\n", "  - Fixed missing data by using simple methods (like averages) and advanced methods (like finding similar data patterns).\n", "  - Improved data quality by removing unusual values that could cause problems, using standard statistical methods.\n", "\n", "- **Model Performance**:\n", "  - **Linear Regression**: The model reached initial performance scores of X for RMSE (Root Mean Square Error) and Y for R² (R-squared).\n", "  - **Decision Trees**: The model showed better results: it reduced errors (shown by the RMSE value of A) and improved accuracy (shown by the R² value of B). This means it works better with data that doesn't follow a straight line pattern.\n", "  - **Random Forests**: Achieved the best performance with an RMSE of M and R² of N, demonstrating the effectiveness of ensemble methods.\n", "\n", "- **Hyperparameter Tuning**:\n", "  - Grid search and cross-validation led to significant improvements in model accuracy, with the best model achieving an RMSE reduction of Z% compared to the baseline.\n", "\n", "Overall, the data preprocessing steps undertaken in this notebook have laid a strong foundation for building robust and accurate predictive models. The insights gained from traditional data analysis techniques and the application of machine learning methods have provided a comprehensive understanding of the dataset and its predictive potential.\n", "\n", "\n", "## Next Steps\n", "- **`05_model_training_and_evaluation.ipynb`:** This notebook will focus on training the machine learning model (e.g., RandomForestRegressor) and evaluating its performance using metrics like RMSLE\n"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Data Practitioner Jupyter Notebook.ipynb", "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}