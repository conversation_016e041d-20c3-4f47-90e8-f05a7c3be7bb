{"cells": [{"cell_type": "markdown", "metadata": {"id": "0aStgWSO0E0E"}, "source": ["# **02. Feature Engineering**\n", "*This notebook will focus on adding extra features like saleYear, saleMonth, etc., derived from the saledate column.*\n"]}, {"cell_type": "markdown", "metadata": {"id": "1eLEkw5O0ECa"}, "source": ["## Objectives\n", "\n", "- Engineer features for modeling bulldozer prices\n", "- To improve our bulldozer price predictions by:\n", "    - Creating helpful new data points\n", "    - Adjusting existing information\n", "    - Picking the most useful details that help predict prices accurately\n", "- To look at our data closely to understand how different pieces of information are connected and organized, which will help us find what's most important for predicting bulldozer prices\n", "\n", "## Inputs\n", "\n", "- Raw bulldozer sales dataset containing sale dates, machine hours, model identifiers, and other relevant attributes\n", "- Extra data to make our analysis better\n", "- A processed dataset with engineered features, ready for model training\n", "- Visualizations and summary statistics showing data insights and feature engineering effectiveness\n", "\n", "## Outputs\n", "\n", "- Dataset prepared and improved for predicting bulldozer prices\n", "- Analysis of which features affect bulldozer prices the most\n", "\n", "## Additional Comments\n", "\n", "- Clean up any data that's missing or unusual before using it\n", "- Document all decisions and their rationales during the data improvement process\n", "- This notebook is a crucial step in our machine learning pipeline, preparing data for accurate and robust predictive models\n", "- **Traditional data analysis techniques include:**\n", "    - **Exploratory Data Analysis (EDA)**: Understanding distributions, relationships, and patterns through summary statistics, visualizations, and outlier identification\n", "    - **Correlation Analysis**: Identifying relevant features by analyzing their relationships with bulldozer prices\n", "    - **Data Cleaning**: Handling missing values, fixing inconsistencies, and formatting data appropriately\n", "- **Machine learning techniques include:**\n", "    - **Feature Engineering**: Creating new features like bulldozer age and usage metrics, and converting categorical variables to numerical forms\n", "    - **Feature Selection**: Using preliminary models to evaluate and select the most important features\n", "    - **Data Transformation**: Applying scaling and normalization to prepare features for model training\n", "\n", "By combining these approaches, we've created a thorough process for preparing our dataset for accurate price prediction models."]}, {"cell_type": "markdown", "metadata": {"id": "9uWZXH9LwoQg"}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Execution Timestamp\n", "\n", "Purpose: This code block adds a timestamp to track notebook execution\n", "- Helps monitor when analysis was last performed\n", "- Ensures reproducibility of results\n", "- Useful for debugging and version control"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebook last run (end-to-end): 2025-03-24 10:32:47.732042\n"]}], "source": ["# Timestamp\n", "import datetime\n", "\n", "import datetime\n", "print(f\"Notebook last run (end-to-end): {datetime.datetime.now()}\")"]}, {"cell_type": "markdown", "metadata": {"id": "cqP-UeN-z3i2"}, "source": ["# Project Directory Structure and Working Directory\n", "\n", "**Purpose: This code block establishes and explains the project organization**\n", "- Creates a standardized project structure for data science workflows\n", "- Documents the purpose of each directory for team collaboration\n", "- Gets current working directory for file path management\n", "\n", "## Key Components:\n", "1. `data/ directory` stores all datasets (raw, processed, interim)\n", "2. `src/` contains all source code (data preparation, models, utilities)\n", "3. `notebooks/` holds <PERSON><PERSON><PERSON> notebooks for experimentation\n", "4. `results/` stores output files and visualizations\n", "\n", "## Project Root Structure\n", "\n", "- **`data/`** - Where all your datasets live\n", "    - `raw/` - Original, untouched data\n", "    - `processed/` - Cleaned and prepared data\n", "    - `interim/` - Temporary data files\n", "- **`src/`** - Your source code\n", "    - `data_prep/` - Code for preparing data\n", "    - `models/` - Your ML models\n", "    - `utils/` - Helper functions\n", "- **`notebooks/`** - <PERSON><PERSON><PERSON> notebooks for experiments\n", "- **`results/`** - Model outputs and visualizations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up Working Directory\n", "This code block sets up the working environment by:\n", "- Changing to the project directory where our code and data files are located\n", "- Verifying the current working directory to ensure we're in the right place"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "wZfF_j-Bz3i4", "outputId": "66943449-1436-4c3d-85c7-b85f9f78349b"}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "# Move to the desired directory\n", "os.chdir('c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2')\n", "\n", "# Get the current directory to verify the change\n", "current_dir = os.getcwd()\n", "current_dir"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set Working Directory to Project Root\n", "**Purpose: Changes the current working directory to the parent directory**\n", "- Gets the folder one level above the current one\n", "- Makes sure all file locations work correctly throughout the project\n", "- Keeps files and folders organized in a clean way"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "TwHsQRWjz3i9", "outputId": "86849db3-cd2f-4cc5-ebb8-2d0caafa1a2c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You set a new current directory\n"]}], "source": ["os.chdir(os.path.dirname(current_dir))\n", "print(\"You set a new current directory\")"]}, {"cell_type": "markdown", "metadata": {"id": "M_xPk_Ijz3i-"}, "source": ["## Get Current Working Directory\n", "**Purpose: Retrieves and stores the current working directory path**\n", "- Gets the folder location where we're currently working\n", "- Saves this location in a variable called current_dir so we can use it later\n", "- Helps us find and work with files in the right place"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "vz3S-_kjz3jA", "outputId": "00b79ae4-75d0-4a96-d193-ac9ef9847ea2"}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "# Change the current working directory\n", "os.chdir('c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository')\n", "\n", "# Get the current working directory\n", "current_dir = os.getcwd()\n", "current_dir"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Import Essential Data Science Libraries and Check Versions**\n", "\n", "**Purpose: This code block imports fundamental Python libraries for data analysis and visualization**\n", "- `pandas:` For data manipulation and analysis\n", "- `numpy:` For numerical computations\n", "- `matplotlib:` For creating visualizations and plots\n", "\n", "**The version checks help ensure:**\n", "- *Code compatibility across different environments*\n", "- *Reproducibility of analysis*\n", "- *Easy debugging of version-specific issues*\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pandas version: 2.2.3\n", "NumPy version: 2.2.4\n", "matplotlib version: 3.10.1\n"]}], "source": ["# Import data analysis tools\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "print(f\"pandas version: {pd.__version__}\")\n", "print(f\"NumPy version: {np.__version__}\")\n", "print(f\"matplotlib version: {matplotlib.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {"id": "-mavJ8DibrcQ"}, "source": ["# **Adding extra features to our DataFrame**\n", "### What is Feature Engineering?\n", "\n", "Feature engineering is a powerful technique that allows us to enhance our dataset by deriving new meaningful information from existing data. In this section, we'll explore how to extract valuable temporal features from our sale dates to improve our analysis.\n", "\n", "### Time-Based Components\n", "\n", "Our approach will focus on breaking down sale dates into multiple time-based components:\n", "\n", "- What year it was sold\n", "- What month it was sold\n", "- What day it was sold\n", "- What day of the week it was sold (like Monday = 1, Tuesday = 2)\n", "- What day of the year it was sold (like January 1st = 1, January 2nd = 2)\n", "\n", "### Data Safety\n", "\n", "To ensure data integrity throughout this process, we'll first create a backup of our original dataset. This precautionary step will allow us to revert any changes if needed.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Creating a Safe Working Copy**\n", "Before we start modifying our dataset, it's crucial to create a backup copy. This ensures we can always return to our original data if needed."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current Directory: c:\\Users\\<USER>\\Dropbox\\1 PROJECT\\VS Code Project Respository\n", "Directories in the project:\n", "About-BulldozerPriceGenius-_BPG-_v2\n", "BulldozerPriceGenuis-BPG-\n", "churnometer\n", "CI-Malaria-Detection\n", "Culture-Project\n", "data\n", "housing-price-data-ml\n", "inputs\n", "job_board_django_ztm\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>\n", "NederLearn_V2\n", "Nederlearn_V3\n", "Nederlearn_V4\n", "NederLearn_V5\n", "PriceBulldozerAI\n", "Recipe-App\n", "Recipe-App-Tutorial\n", "Recipe-Tutorial-Dee-MC\n", "results\n", "Scartch-Pad\n", "ZTM-Django-bitly-forms\n", "ztm_bd\n", "ztm_django_bitly_clone_project\n", "ztm_django_jobs_board\n", "ztm_django_movie_app\n"]}], "source": ["import os\n", "\n", "# Check the current directory\n", "current_directory = os.getcwd()\n", "print(f\"Current Directory: {current_directory}\")\n", "\n", "# List all directories in the project\n", "project_directory = current_directory  # Change this if your project directory is different\n", "directories = [d for d in os.listdir(project_directory) if os.path.isdir(os.path.join(project_directory, d))]\n", "\n", "print(\"Directories in the project:\")\n", "for directory in directories:\n", "    print(directory)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## File Path Verification Code\n", "\n", "This code block serves two essential purposes:\n", "\n", "- Verifies the existence of our training dataset (`TrainAndValid.csv`) before attempting to use it\n", "- Provides immediate feedback about file accessibility, helping prevent data loading errors"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The file C:\\Users\\<USER>\\Dropbox\\1 PROJECT\\VS Code Project Respository\\About-BulldozerPriceGenius-_BPG-_v2\\data\\raw\\bluebook-for-bulldozers\\TrainAndValid.csv exists.\n"]}], "source": ["import os\n", "\n", "file_path = \"C:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2\\\\data\\\\raw\\\\bluebook-for-bulldozers\\\\TrainAndValid.csv\"\n", "\n", "# Check if file exists in specified path\n", "if os.path.exists(file_path):\n", "    print(f\"The file {file_path} exists.\")\n", "else:\n", "    print(f\"The file {file_path} does not exist.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Folder Path Verification\n", "\n", "This code block is designed to verify the existence of our processed data folder, which is crucial for data management and integrity. It performs two key functions:\n", "\n", "- Checks if the specified folder path exists in our project structure\n", "- Provides immediate feedback about folder accessibility to prevent data storage/retrieval errors"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The folder C:\\Users\\<USER>\\Dropbox\\1 PROJECT\\VS Code Project Respository\\About-BulldozerPriceGenius-_BPG-_v2\\data\\processed exists.\n"]}], "source": ["import os\n", "\n", "folder_path = \"C:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2\\\\data\\\\processed\"\n", "\n", "# Check if folder exists in specified path\n", "if os.path.exists(folder_path):\n", "    print(f\"The folder {folder_path} exists.\")\n", "else:\n", "    print(f\"The folder {folder_path} does not exist.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading and Preprocessing the Bulldozer Dataset\n", "\n", "This code block performs essential data loading and preprocessing steps:\n", "\n", "- Reads the raw bulldozer price dataset using pandas, with specific configurations:\n", "    - Disables low memory mode to handle mixed data types\n", "    - Automatically parses the 'saledate' column as datetime\n", "- Creates a safe working copy of the data to prevent modifications to the original dataset\n", "- Saves the preprocessed dataset to our processed data folder for further analysis"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 412698 entries, 0 to 412697\n", "Data columns (total 53 columns):\n", " #   Column                    Non-Null Count   Dtype         \n", "---  ------                    --------------   -----         \n", " 0   SalesID                   412698 non-null  int64         \n", " 1   SalePrice                 412698 non-null  float64       \n", " 2   MachineID                 412698 non-null  int64         \n", " 3   ModelID                   412698 non-null  int64         \n", " 4   datasource                412698 non-null  int64         \n", " 5   auctioneerID              392562 non-null  float64       \n", " 6   YearMade                  412698 non-null  int64         \n", " 7   MachineHoursCurrentMeter  147504 non-null  float64       \n", " 8   UsageBand                 73670 non-null   object        \n", " 9   saledate                  412698 non-null  datetime64[ns]\n", " 10  fiModelDesc               412698 non-null  object        \n", " 11  fiBaseModel               412698 non-null  object        \n", " 12  fiSecondaryDesc           271971 non-null  object        \n", " 13  fiModelSeries             58667 non-null   object        \n", " 14  fiModelDescriptor         74816 non-null   object        \n", " 15  ProductSize               196093 non-null  object        \n", " 16  fiProductClassDesc        412698 non-null  object        \n", " 17  state                     412698 non-null  object        \n", " 18  ProductGroup              412698 non-null  object        \n", " 19  ProductGroupDesc          412698 non-null  object        \n", " 20  Drive_System              107087 non-null  object        \n", " 21  Enclosure                 412364 non-null  object        \n", " 22  Forks                     197715 non-null  object        \n", " 23  Pad_Type                  81096 non-null   object        \n", " 24  Ride_Control              152728 non-null  object        \n", " 25  Stick                     81096 non-null   object        \n", " 26  Transmission              188007 non-null  object        \n", " 27  Turbocharged              81096 non-null   object        \n", " 28  Blade_Extension           25983 non-null   object        \n", " 29  <PERSON><PERSON>Width               25983 non-null   object        \n", " 30  Enclosure_Type            25983 non-null   object        \n", " 31  Engine_Horsepower         25983 non-null   object        \n", " 32  Hydraulics                330133 non-null  object        \n", " 33  Pushblock                 25983 non-null   object        \n", " 34  <PERSON><PERSON><PERSON>                    106945 non-null  object        \n", " 35  Scarifier                 25994 non-null   object        \n", " 36  Tip_Control               25983 non-null   object        \n", " 37  Tire_Size                 97638 non-null   object        \n", " 38  <PERSON>upler                   220679 non-null  object        \n", " 39  Coupler_System            44974 non-null   object        \n", " 40  Grouser_Tracks            44875 non-null   object        \n", " 41  Hydraulics_Flow           44875 non-null   object        \n", " 42  Track_Type                102193 non-null  object        \n", " 43  Undercarriage_Pad_Width   102916 non-null  object        \n", " 44  Stick_Length              102261 non-null  object        \n", " 45  Thumb                     102332 non-null  object        \n", " 46  Pattern_Changer           102261 non-null  object        \n", " 47  Grouser_Type              102193 non-null  object        \n", " 48  Backhoe_Mounting          80712 non-null   object        \n", " 49  Blade_Type                81875 non-null   object        \n", " 50  Travel_Controls           81877 non-null   object        \n", " 51  Differential_Type         71564 non-null   object        \n", " 52  Steering_Controls         71522 non-null   object        \n", "dtypes: datetime64[ns](1), float64(3), int64(5), object(44)\n", "memory usage: 166.9+ MB\n"]}], "source": ["import os\n", "import pandas as pd\n", "\n", "# Define the file path\n", "file_path = \"../data/raw/bluebook-for-bulldozers/TrainAndValid.csv\"\n", "\n", "# Use chunksize to read the file in smaller chunks\n", "chunksize = 100000  # Adjust the chunk size as needed\n", "chunks = []\n", "\n", "# Read the file in chunks and concatenate them\n", "for chunk in pd.read_csv(filepath_or_buffer=file_path, \n", "                         low_memory=False, \n", "                         parse_dates=[\"saledate\"], \n", "                         chunksize=chunksize):\n", "    chunks.append(chunk)\n", "\n", "# Combine all chunks into a single DataFrame\n", "df = pd.concat(chunks, ignore_index=True)\n", "\n", "# Check the DataFrame info\n", "df.info()\n", "\n", "# Make a copy of the original DataFrame to perform edits on\n", "df_tmp = df.copy()\n", "\n", "# Save the copy to the processed folder\n", "processed_file_path = \"C:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2\\\\data\\\\processed\\\\TrainAndValid_processed.csv\"\n", "df_tmp.to_csv(processed_file_path, index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## File Existence Verification\n", "\n", "This code block performs a crucial check to ensure data availability and prevent potential errors:\n", "\n", "- Imports the 'os' module for file system operations\n", "- Defines the exact path to our processed dataset using a raw string (r\"...\")\n", "- Uses os.path.exists() to verify if the file is present at the specified location\n", "- Provides clear feedback about whether the file was found or not"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File exists.\n"]}], "source": ["import os\n", "\n", "file_path = r\"C:\\Users\\<USER>\\Dropbox\\1 PROJECT\\VS Code Project Respository\\About-BulldozerPriceGenius-_BPG-_v2\\data\\processed\\TrainAndValid_processed.csv\"\n", "\n", "if os.path.exists(file_path):\n", "    print(\"File exists.\")\n", "else:\n", "    print(\"File does not exist.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Breaking Down Sales Dates into Useful Information\n", "\n", "This code takes the sale dates in our bulldozer dataset and breaks them down into useful pieces:\n", "\n", "### Data Loading\n", "\n", "- Reading a processed CSV file containing bulldozer sales data\n", "\n", "### Date Component Extraction\n", "\n", "- Breaking down the sale date into its basic parts, such as:\n", "    - Year of sale\n", "    - Month of sale\n", "    - Day of sale\n", "    - Day of the week\n", "    - Day of the year\n", "\n", "### Data Processing\n", "\n", "- Deleting the original date column since we already created the separate date components we need\n", "- Storing the improved dataset in the same folder where we keep our processed files\n", "\n", "### Purpose\n", "\n", "- Breaks down sales dates into meaningful components:\n", "    - Year, month, and day\n", "    - Day of week and day of year\n", "- Helps model identify price patterns:\n", "    - More granular time analysis\n", "    - Better pattern recognition\n", "- Ensures data integrity:\n", "    - Verifies file locations\n", "    - Handles potential errors"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9364\\1532147682.py:12: DtypeWarning: Columns (11,13,14,15,22,23,24,25,27,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(file_path, parse_dates=['saledate'], chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9364\\1532147682.py:12: DtypeWarning: Columns (11,39,40,41) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(file_path, parse_dates=['saledate'], chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9364\\1532147682.py:12: DtypeWarning: Columns (13) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(file_path, parse_dates=['saledate'], chunksize=chunk_size):\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File processed successfully\n"]}], "source": ["import pandas as pd\n", "import os\n", "\n", "# Define file path\n", "file_path = r\"C:\\Users\\<USER>\\Dropbox\\1 PROJECT\\VS Code Project Respository\\About-BulldozerPriceGenius-_BPG-_v2\\data\\processed\\TrainAndValid_processed.csv\"\n", "\n", "# Verify file exists\n", "if os.path.exists(file_path):\n", "    # Process the file in chunks\n", "    chunk_size = 100000  # Adjust chunk size as needed\n", "    chunks = []\n", "    for chunk in pd.read_csv(file_path, parse_dates=['saledate'], chunksize=chunk_size):\n", "        # Add datetime parameters for saledate\n", "        chunk[\"saleYear\"] = chunk.saledate.dt.year\n", "        chunk[\"saleMonth\"] = chunk.saledate.dt.month\n", "        chunk[\"saleDay\"] = chunk.saledate.dt.day\n", "        chunk[\"saleDayofweek\"] = chunk.saledate.dt.dayofweek\n", "        chunk[\"saleDayofyear\"] = chunk.saledate.dt.dayofyear\n", "        \n", "        # Drop original saledate column\n", "        chunk.drop(\"saledate\", axis=1, inplace=True)\n", "        \n", "        # Append processed chunk to the list\n", "        chunks.append(chunk)\n", "    \n", "    # Concatenate all chunks into a single DataFrame\n", "    df_tmp = pd.concat(chunks, ignore_index=True)\n", "    \n", "    # Save the modified DataFrame back to CSV\n", "    df_tmp.to_csv(file_path, index=False)\n", "    print(\"File processed successfully\")\n", "else:\n", "    print(\"File does not exist\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Viewing Date Feature Columns\n", "\n", "This code shows us a preview of our new date columns along with the SalePrice. This helps us check if we correctly created our new date features by displaying them in a table.\n", "\n", "- The original SalePrice column for reference\n", "- All new date-based columns we created:\n", "    - Year, month, and day of sale\n", "    - Day of week and day of year"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SalePrice</th>\n", "      <th>saleYear</th>\n", "      <th>saleMonth</th>\n", "      <th>saleDay</th>\n", "      <th>saleDayofweek</th>\n", "      <th>saleDayofyear</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>66000.0</td>\n", "      <td>2006</td>\n", "      <td>11</td>\n", "      <td>16</td>\n", "      <td>3</td>\n", "      <td>320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>57000.0</td>\n", "      <td>2004</td>\n", "      <td>3</td>\n", "      <td>26</td>\n", "      <td>4</td>\n", "      <td>86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10000.0</td>\n", "      <td>2004</td>\n", "      <td>2</td>\n", "      <td>26</td>\n", "      <td>3</td>\n", "      <td>57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>38500.0</td>\n", "      <td>2011</td>\n", "      <td>5</td>\n", "      <td>19</td>\n", "      <td>3</td>\n", "      <td>139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>11000.0</td>\n", "      <td>2009</td>\n", "      <td>7</td>\n", "      <td>23</td>\n", "      <td>3</td>\n", "      <td>204</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   SalePrice  saleYear  saleMonth  saleDay  saleDayofweek  saleDayofyear\n", "0    66000.0      2006         11       16              3            320\n", "1    57000.0      2004          3       26              4             86\n", "2    10000.0      2004          2       26              3             57\n", "3    38500.0      2011          5       19              3            139\n", "4    11000.0      2009          7       23              3            204"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# View newly created columns\n", "df_tmp[[\"SalePrice\", \"saleYear\", \"saleMonth\", \"saleDay\", \"saleDayofweek\", \"saleDayofyear\"]].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing SalePrice Distribution\n", "\n", "Let's explore the distribution of bulldozer prices in our dataset using a histogram:\n", "\n", "### Price Distribution Histogram\n", "\n", "The histogram helps us understand:\n", "\n", "- How bulldozer prices are distributed\n", "- The most common price ranges\n", "- Whether there are more low-priced or high-priced bulldozers\n", "- Any unusual prices that might need special attention\n", "\n", "### What We Learn\n", "We can:\n", "\n", "- See how prices change across different time periods\n", "- Understand what prices are most typical in the market\n", "- Identify if we need to adjust our data to make it work better with our prediction models\n", "- Spot any market trends that might affect bulldozer values\n", "\n", "This information helps us build a better price prediction model by understanding how bulldozer prices typically behave in the market."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# View SalePrice distribution \n", "df.SalePrice.plot.hist(xlabel=\"Sale Price ($)\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing Sale Price Distribution by Month\n", "\n", "Here are the key points about the scatter plot visualization:\n", "- Creates a visualization of sale prices vs. months\n", "- Uses a sample of 10,000 data points to maintain clarity and prevent overcrowding\n", "- Helps identify seasonal patterns and trends in bulldozer prices throughout the year"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# View 10,000 samples SalePrice against saleMonth\n", "fig, ax = plt.subplots()\n", "ax.scatter(x=df_tmp[\"saleMonth\"][:10000], # visualize the first 10000 values\n", "           y=df_tmp[\"SalePrice\"][:10000])\n", "ax.set_xlabel(\"Sale Month\")\n", "ax.set_ylabel(\"Sale Price ($)\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing Monthly Price Trends\n", "\n", "This code makes a graph that shows typical bulldozer prices for each month. We look at the average price per month to:\n", "\n", "- Identify seasonal pricing patterns\n", "    - Spot months with typically higher or lower prices\n", "    - Help buyers and sellers make more informed decisions"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAk0AAAGwCAYAAAC0HlECAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAZD9JREFUeJzt3Qd4U+X3B/DTvQelm1LaUkZLoZS9BBEEWYIbUFHcCiqgLBVQQVD4Of4CigtwIeAAGbKEspdsCm3Zu6WUQvdu/s952xsaKJC0Se69yffzPKEZt+nLLSEn73vec2w0Go2GAAAAAOCObO/8MAAAAAAwBE0AAAAAekDQBAAAAKAHBE0AAAAAekDQBAAAAKAHBE0AAAAAekDQBAAAAKAHe30OgrsrKyujS5cukYeHB9nY2Mg9HAAAANADl6vMzs6m4OBgsrW981wSgiYj4YCpbt26cg8DAAAAquH8+fMUEhJyx2MQNBkJzzBJJ93T01Pu4QAAAIAesrKyxKSH9D5+JwiajERakuOACUETAACAuuiTWoNEcAAAAAA9IGgCAAAA0AOCJgAAAAA9IGgCAAAA0AOCJgAAAAA9IGgCAAAA0AOCJgAAAAA9IGgCAAAA0AOCJgAAAAA9IGgCAAAAUHrQNG3aNGrdurXo9+Lv708DBgyg5OTkW47bsWMH3XfffeTm5iZalHTu3Jny8/O1j2dkZNCTTz4pHvP29qbnn3+ecnJydJ7j0KFDdM8995Czs7PoMTN9+vRbfs7vv/9OjRs3Fsc0bdqU/vnnHxP9zQEAAEBtZA2aNm3aRMOGDaOdO3fSunXrqLi4mHr06EG5ubk6AdMDDzwg7t+9ezf9999/NHz4cLK1vTF0DpiOHDkinmPFihW0efNmeumll3Sa8fH316tXj/bu3UszZsyg999/n7799lvtMdu3b6dBgwaJgGv//v0igONLQkKCGc8IAAAAKJWNRqPRkEJcuXJFzDhxMMWzSaxdu3Z0//330+TJk6v8nsTERIqOjhbBVKtWrcR9q1evpt69e9OFCxcoODiYvv76a3r33XcpNTWVHB0dxTHjxo2jpUuXUlJSkrj9xBNPiGCNgy4J/+zmzZvTnDlz7jp2Dsy8vLwoMzPTqA1784pKKCO3iJTO3taWAjyd9Gp4CAAAoBSGvH/bk4LwgJmPj4/4mpaWRrt27RIzSR06dKCTJ0+K5bOPPvqIOnXqpJ2J4iU5KWBi3bt3FzNR/L0PPfSQOIaDMClgYj179qRPPvmErl27RrVq1RLHjBo1Smc8fAwHVlUpLCwUl8on3RT+TUyjN37bT2rwQqdweq9vtNzDAAAAsOxE8LKyMhoxYgR17NiRYmJixH2nTp0SX3kp7cUXXxQzSC1atKBu3brR8ePHxWM8e8SzU5XZ29uLwIsfk44JCAjQOUa6fbdjpMerysfiyFS6cJ6UKdjZ2JCTva2iL4525f+M/k28bJJzAAAAoASKmWni3CbOH9q6datOIMVefvllGjp0qLgeFxdH69evp7lz54rARS7jx4/XmZnimSZTBE59mgWJi5Kl5xRSqyn/0tmMPMotLCE3J8X8swIAADAaRby7cWK3lMAdEhKivT8oqDxY4JylyqKioujcuXPiemBgoFjGq6ykpETsqOPHpGMuX9adBZFu3+0Y6fGbOTk5iQsQ+bo7kZ+HE13JLqTky9nUIrSW3EMCAACwrOU5zkHngGnJkiW0YcMGCg8P13k8LCxMJHLfXIbg2LFjYicca9++PV2/fl3sipPwc/EsVdu2bbXHcEDGu/MkvNOuUaNGIp9JOoZnsCrjY/h+uLuooPLkucQU0+R2AQAAWHXQxEtyv/zyCy1YsEDUauL8Ib5INZh4J9bo0aPpyy+/pD/++INOnDhBEyZMEDveuDSANOvEJQk454lLEmzbtk0EYgMHDhQBFxs8eLBIAufv4dIEixYtov/7v//TWV578803Rc7Up59+Kp6f86j27NkjngvuLirIQ3xF0AQAABZLIyP+8VVd5s2bp3PctGnTNCEhIRpXV1dN+/btNVu2bNF5/OrVq5pBgwZp3N3dNZ6enpqhQ4dqsrOzdY45ePCgplOnThonJydNnTp1NB9//PEt41m8eLGmYcOGGkdHR02TJk00K1eu1PvvkpmZKcbOX63R0v0XNPXGrtA8/NU2uYcCAABgkvdvRdVpUjNT1WlSi+TUbOr5xWZyc7Sjw+/3JFtb1GsCAADLev9WTMkBULcIPzdReiC3qJTOX8uTezgAAABGh6AJjMLBzpYaBLiL68hrAgAAS4SgCUywgy5b7qEAAAAYHYImMBqUHQAAAEuGoAmMX3YgFUETAABYHgRNYDRRgeUzTecz8im74EYhUQAAAEuAoAmMppabIwV6OovrSanIawIAAMuCoAmMCpXBAQDAUiFoAqNCMjgAAFgqBE1gVCg7AAAAlgpBE5gkaOK2KqVl6NADAACWA0ETGFW4rxs52dtSfnEpnb2aK/dwAAAAjAZBExiVna0NNQqUksGxRAcAAJYDQROYrF4TksEBAMCSIGgCo0PZAQAAsEQImsBkyeAocAkAAJYEQRMYXeOKoOni9XzKzEM7FQAAsAwImsDovFwcqI63i7iO5r0AAGApEDSBSaAyOAAAWBoETWASSAYHAABLg6AJTALtVAAAwNIgaALTtlO5nE0lpWVyDwcAAKDGEDSBSdTzcSVXRzsqKimjM2inAgAAFgBBE5iEbaV2KkexRAcAABYAQROYDHbQAQCAJUHQBCYTpW3ci6AJAADUD0ETmAxmmgAAwJIgaAKTt1O5nFVIGblFcg8HAACgRhA0gcm4O9lTqI+ruJ6E2SYAAFA5BE1glsrgRxE0AQCAyiFoApNCZXAAALAUCJrApJAMDgAAlgJBE5hUVGB50HQiLYeK0U4FAABUDEETmFRILReREF5UWkYnr+TIPRwAAIBqQ9AEJm+n0hhFLgEAwAIgaAKz5TUlIRkcAABUDEETmC1oQtkBAABQMwRNYLZaTSg7AAAAaoagCUyuUaAH2dgQpecU0pXsQrmHAwAAUC0ImsDkXB3tKay2m7iOZHAAAFArBE1g5iU6BE0AAKBOCJrArEUuETQBAIBaIWgC85YdSEUyOAAAqBOCJjCLqOAb7VQKS0rlHg4AAIDBEDSBWQR7OZOnsz2VlGlE4AQAAKA2sgZN06ZNo9atW5OHhwf5+/vTgAEDKDk5WeeYe++9l2xsbHQur7zyis4xNz/Ol4ULF+ocs3HjRmrRogU5OTlRZGQkzZ8//5bxzJ49m8LCwsjZ2Znatm1Lu3fvNtHf3Prw76RxxRId6jUBAIAayRo0bdq0iYYNG0Y7d+6kdevWUXFxMfXo0YNyc3N1jnvxxRcpJSVFe5k+ffotzzVv3jydYzgAk5w+fZr69OlDXbt2pQMHDtCIESPohRdeoDVr1miPWbRoEY0aNYomTZpE+/bto9jYWOrZsyelpaWZ+CxYj2ht0IRkcAAAUB97OX/46tWrdW7z7A/POO3du5c6d+6svd/V1ZUCAwPv+Fze3t63PWbOnDkUHh5On376qbgdFRVFW7dupc8//1wERuyzzz4TwdnQoUO137Ny5UqaO3cujRs3rsZ/V0DZAQAAUDdF5TRlZmaKrz4+Pjr3//rrr+Tr60sxMTE0fvx4ysvLu+V7ecaKj2nTpo0IdDQajfaxHTt2UPfu3XWO52CJ72dFRUUiUKt8jK2trbgtHXOzwsJCysrK0rmAfjvoOGiq/PsBAABQA1lnmiorKysTy2YdO3YUwZFk8ODBVK9ePQoODqZDhw7R2LFjRd7TX3/9pT3mww8/pPvuu0/MSK1du5Zee+01ysnJoTfeeEM8npqaSgEBATo/j29zoJOfn0/Xrl2j0tLSKo9JSkq6bT7WBx98YOSzYNkaBniQrQ3RtbxiSssupABPZ7mHBAAAoL6giWeKEhISxLJZZS+99JL2etOmTSkoKIi6detGJ0+epPr164v7J0yYoD0mLi5O5ETNmDFDGzSZAs94cQ6UhAOwunXrmuznWQJnBzuK8HMXu+eOpmQhaAIAAFVRxPLc8OHDacWKFRQfH08hISF3PJZ3tbETJ07c8ZgLFy6IJTTGuU6XL1/WOYZve3p6kouLi1jWs7Ozq/KY2+VJ8S48/v7KFzBsiQ4AAEBNZA2aOK+FA6YlS5bQhg0bRLL23fDuN8YzTnc6platWiKwYe3bt6f169frHMO79fh+5ujoSC1bttQ5hpcL+bZ0DBhH40ApGRxlBwAAQF3s5V6SW7BgAf3999+iVhPnHjEvLy8xA8RLcPx47969qXbt2iKnaeTIkWJnXbNmzcSxy5cvFzNC7dq1E/WVOBiaOnUqvf3229qfw3WdZs2aRWPGjKHnnntOBGiLFy8Wu+MkvNT2zDPPUKtWrUQy+RdffCGW+aTddGAcKDsAAACqpZER//iqLvPmzROPnzt3TtO5c2eNj4+PxsnJSRMZGakZPXq0JjMzU/scq1at0jRv3lzj7u6ucXNz08TGxmrmzJmjKS0t1flZ8fHx4jhHR0dNRESE9mdUNnPmTE1oaKg4pk2bNpqdO3fq/XfhMfHYK48NbpVyPV9Tb+wKTfi4FZr8ohK5hwMAAFYu04D3bxv+Q+7AzRJwIjjPkHHZBOQ33R7/c2sxeZ3YQbd8eCdqGuIl95AAAMCKZRnw/q2IRHCwrnYqSAYHAAA1QtAEZicFTVx2AAAAQC0QNIGMO+gQNAEAgHogaAKzQzsVAABQIwRNYHYNAtzJ3taGsgpK6FJmgdzDAQAA0AuCJjA7J3s7qu/nLq4nYYkOAABUAkETyCIqCHlNAACgLgiaQOa8JrRTAQAAdUDQBLJArSYAAFAbBE0gi8YVy3Onr+ZSXlGJ3MMBAAC4KwRNIAt/D2fydXckrjiQnIolOgAAUD4ETSAb5DUBAICaIGgC2YOmpFTkNQEAgPIhaALZoOwAAACoCYImkH+mKSUb7VQAAEDxEDSBbCJ83cnBzoayC0vowrV8uYcDAABwRwiaQDaO9rYU6V++RHcUS3QAAKBwCJpAVshrAgAAtUDQBLKKRmVwAABQCQRNoJCyA6jVBAAAyoagCRQRNJ29mkc5hWinAgAAyoWgCWTl4+ZIAZ5O4noyilwCAICCIWgC2TUOLJ9tOop2KgAAoGAImkBBPegw0wQAAMqFoAlkh7IDAACgBgiaQDFlB5JTs6msDO1UAABAmRA0gezCfd1EdfC8olI6l5En93AAAACqhKAJZGdvZ0uNArBEBwAAyoagCRQBeU0AAKB0CJpAEVB2AAAAlA5BEygCyg4AAIDSIWgCRe2gu3g9nzLzi+UeDgAAwC0QNIEieLk6ULCXs7b0AAAAgNIgaALFwBIdAAAomb0hBycmJtLChQtpy5YtdPbsWcrLyyM/Pz+Ki4ujnj170iOPPEJOTuXNVwGqEzStT0pD0AQAAOqdadq3bx91795dBEdbt26ltm3b0ogRI2jy5Mn01FNPkUajoXfffZeCg4Ppk08+ocLCQtOPHCxOY5QdAAAAtc808QzS6NGj6Y8//iBvb+/bHrdjxw76v//7P/r000/pnXfeMeY4wYqW55IvZ1NpmYbsbG3kHhIAAIBhQdOxY8fIwcHhrse1b99eXIqLsfsJDBdW242cHWypoLiMTqfnUqS/u9xDAgAAMGx5Tp+AqSbHAzCeWWpUUeQyKRVLdAAAYGG75/7991+aNGkSLV++3DgjAqsWjbwmAACwhKDptddeowkTJmhv//nnn/TAAw/QypUr6YknnqDPPvvMFGMEqyw7gFpNAACg4qApPj6eOnfurL3NQdLUqVNpz5499Msvv9BXX31lijGCFUGtJgAAUHUi+AcffCC+njt3jv7++2+xS47LDPz3338UGxtLH374IRUUFIjH+TqbOHGiaUcOFqlRYPnyXEpmAV3PKyJvV0e5hwQAAKB/0PTss8+Kr3PmzKH777+fmjdvLgpcBgYG0rhx40QAlZubS19++aU4lm8DVIenswOF1HKhC9fy6WhKFnWo7yv3kAAAAPRfnqtXr564tGvXjmbMmEHbt2+nmTNn0kMPPUShoaHiMQ6awsPDtbcBqgt5TQAAoPqcps8//5xsbGzopZdeIh8fH7FrTvLNN99Qv379DPrh06ZNo9atW5OHhwf5+/vTgAEDKDk5WeeYe++9V/zMypdXXnlF5xheFuzTpw+5urqK5+FCnCUlJTrHbNy4kVq0aCHavERGRtL8+fNvGc/s2bMpLCyMnJ2dRdXz3bt3G/T3AeMGTUnIawIAALX2nuOAgpflqvL9998b/MM3bdpEw4YNE4ETBzlcRbxHjx509OhRcnNz0x734osvanOlGAdHktLSUhEw8VIhz4ClpKTQkCFDRK0oTlJnp0+fFsdwsPXrr7/S+vXr6YUXXqCgoCDRM48tWrSIRo0aJZYgOWD64osvxGMcxHEgBjKUHUCtJgAAUBAbjYISkK5cuSICFA6mpF16PNPEOVQcxFRl1apV1LdvX7p06RIFBASI+zjwGTt2rHg+R0dHcZ3LIiQkJGi/b+DAgXT9+nVavXq1uM2BEgdvs2bNErfLysqobt269Prrr4u8rbvJysoiLy8vyszMJE/P8pkSqJ6zV3Opy4yN5GhvS0c/6En2djUuJwYAAFDj92+93o0+/vhjys/P1+dQ2rVrlwhQqoMHzHjprzKeHfL19aWYmBgaP3485eXlaR/jnXxNmzbVBkyMZ4j4JBw5ckR7DDccroyP4ftZUVER7d27V+cYW1tbcVs65mbclJh/RuULGEfdWq7k5mhHRSVldCo9V+7hAAAA6B808XIZJ3hzcUue2eEZHAkvqx06dEjUaOrQoYMocsk5SobimZ0RI0ZQx44dRXAkGTx4sKgBxTWiOGD6+eef6amnntI+npqaqhMwMek2P3anYzjQ4WAwPT1dLPNVdYz0HFXlY3FkKl14VgqMw1a0U0FlcAAAUGFO008//UQHDx4US1ccxHCwYWdnJ5KqpVmfuLg4kSfEJQc4kdpQnNvEy2dbt27VuZ+TziU8o8R5SN26daOTJ09S/fr1SS4cwHEOlITPCQIn4yaD7zt3XZQd6N+8jtzDAQAA0D8RnItYfvfdd2KXHM8snT17VszS8LIZ5xzx1+oaPnw4rVixgjZv3kwhISF3PJZzj9iJEydE0MQJ4Dfvcrt8+bL4yo9JX6X7Kh/Da5cuLi4iAORLVcdIz3EzDhj5AqaBsgMAAKA0BmfYcq4PB0n9+/cXydSc91PdgIlz0DlgWrJkCW3YsEHUebqbAwcOiK8848Tat29Phw8fprS0NO0x69atEwFRdHS09hjeMVcZH8P3M04Wb9mypc4xvFzIt6VjwLxQdgAAAJRG1m1JvCTH+UoLFiwQeVCcP8QXKemcl+AmT54skrTPnDlDy5YtE+UEeGdds2bNxDFcooCDo6efflosIa5Zs4bee+898dzSTBCXGjh16hSNGTOGkpKSRP7V4sWLaeTIkdqx8FIbz6T9+OOPlJiYSK+++qoo2Dl06FCZzo51axzoQTY2RGnZhXQ1p1Du4QAAAIjZHtnwj6/qMm/ePPH4uXPnNJ07d9b4+PhonJycNJGRkZrRo0drMjMzdZ7nzJkzml69emlcXFw0vr6+mrfeektTXFysc0x8fLymefPmGkdHR01ERIT2Z1Q2c+ZMTWhoqDimTZs2mp07d+r9d+Ex8dhvHhtUX5fpGzT1xq7QbDl2Re6hAACAhTLk/VtRdZrUDHWajO+Vn/fS6iOp9G7vKHqxc4TcwwEAAAtk9DpNAPImgyOvCQAA5FftoIl3r3H+kJR/hAkrMLaoinYqXHYAAABAdUHT1atXxY65hg0bUu/evUWvN/b888/TW2+9ZYoxgpXPNJ28kiOqgwMAAKgqaOIdZ/b29nTu3DmdxrlcCVzq4wZgDCG1XMjD2Z6KSzUicAIAAFBV0LR27Vr65JNPbilC2aBBA1HwEsBYbGxsKCoQeU0AAKDSoIlrF1WeYZJkZGSgQjaYLK8JQRMAAKguaLrnnntEL7rKswFcPXv69OnUtWtXY48PrFxjtFMBAAC19Z6TcHDEDXP37NlDRUVFosr2kSNHxEzTtm3bTDNKsFqVyw7wDk0O0gEAAFQx0xQTE0PHjh2jTp06if5zvFz38MMP0/79+0UDXQBjahTgQbY2RFdzi+hKNtqpAACAimaaGFfOfPfdd40/GoCbuDjaUZivG526kkuJqdnk7+ks95AAAMBKGTzTNG/ePPr9999vuZ/v42a3AMaGyuAAAKDKoGnatGnk6+t7y/3+/v40depUY40LQCsaQRMAAKgxaOKiluHh4bfcX69ePfEYgLE1DkTZAQAAUGHQxDNKhw4duuX+gwcPUu3atY01LoAq2qnkUkFxqdzDAQAAK2Vw0DRo0CB64403KD4+nkpLS8Vlw4YN9Oabb9LAgQNNM0qwakFezuTl4kClZRo6kYZ2KgAAoJLdc5MnT6YzZ86IWk3cg45xccshQ4YgpwlM104lyIN2nsoQS3QxdbzkHhIAAFghg4MmR0dHWrRokQieeEnOxcWFmjZtKnKaAEy5RFceNKEyOAAAqKhOE2vYsKG4AJgDyg4AAIAqgqZRo0aJmSU3Nzdx/U4+++wzY40N4NayA6lopwIAAAoOmrhFSnFxsbi+b9++275h4Y0MTCXS353sbG3oel4xpWYVUJCXi9xDAgAAK6NX0MQ75SQbN2405XgAquTsYEcRvm50PC1HLNEhaAIAAEWXHODZJt4xl5CQYLoRAdw1rwnJ4AAAoPCgycHBgUJDQ0VtJgBzQzI4AACoqrjlu+++S++88w5lZGSYZkQAt8G1mhiCJgAAUEXJgVmzZtGJEycoODhY1GbiHXWVcaI4gCl30J1OL2+nwnlOAAAAig2a+vfvj11yIAs/DyfycXOkjNwiSk7Npti63nIPCQAArIjBQdP7779vmpEA6NlOZduJq2KJDkETAAAoMqcpNzeXXn31VapTpw75+fmJ5rxXrlwx7egAbhIViGRwAABQeNA0YcIE+vnnn6lv3740ePBg2rBhA7300kumHR3A7XbQpaLsAAAAKHR5bsmSJTRv3jx67LHHxO0hQ4ZQu3btqKSkRNRuAjB32QG0UwEAAEXONF24cIE6duyovd2yZUtRt+nSpUumGhtAle1UHOxsKLughC5ez5d7OAAAYEX0DprKyspEkFQZzzCh0CWYk6O9LdX3cxfXURkcAADMSe91NV4K6datm85SXF5eHvXr148cHR2196FOE5hjiS4pNVss0d0fHSD3cAAAwEroHTRNmjSpyppNAObGZQeW7McOOgAAUFHQBCAH9KADAABV9J4DUErQdDYjj3ILS+QeDgAAWAkETaA6vu5OoqWKRkOUfBnJ4AAAYB4ImkCVsEQHAADmhqAJVJsMzhA0AQCAKoKmgoIC440EoFo96LA8BwAACg2auMjl5MmTReNed3d3OnXqlLY33Q8//GCKMQLcdnkuKSWLyso0cg8HAACsgMFB05QpU2j+/Pk0ffp0naKWMTEx9P333xt7fABVivBzI0c7W8otKqXz1/LkHg4AAFgBg4Omn376ib799lt68sknyc7OTnt/bGwsJSUlGXt8AFVysLOlBgFopwIAAAoOmi5evEiRkZFVLtsVFxcba1wAd4UddAAAoOigKTo6mrZs2XLL/X/88QfFxcUZa1wAd4WgCQAAFB00TZw4kYYPH06ffPKJmF3666+/6MUXX6SPPvpIPGaIadOmUevWrcnDw4P8/f1pwIABlJycfNuGwb169SIbGxtaunSpzmN8382XhQsX6hyzceNGatGiBTk5OYmZMs7Lutns2bMpLCyMnJ2dqW3btrR7926D/j5gXlGBFWUHUhE0AQCAAoMmbtK7fPly+vfff8nNzU0ESomJieK++++/36Dn2rRpEw0bNox27txJ69atE8t7PXr0oNzc3FuO/eKLL0QwdDvz5s2jlJQU7YUDMMnp06epT58+1LVrVzpw4ACNGDGCXnjhBVqzZo32mEWLFtGoUaNEj719+/aJHK2ePXtSWlqaQX8nMP9M0/mMfMouwNIwAACYlo2Gp3AU4sqVK2LGiYOpzp07a+/nQKdv3760Z88eCgoKoiVLlugERRxM3XxfZWPHjqWVK1dSQkKC9r6BAwfS9evXafXq1eI2zyzxrNesWbPEbZ5Fq1u3Lr3++us0bty4u449KyuLvLy8KDMzkzw9y9/MwfTaTV1PqVkF9Psr7al1mI/cwwEAAJUx5P1bURXBecDMx+fGm19eXh4NHjxYLJ0FBgbe9nt5xsrX15fatGlDc+fOFct5kh07dlD37t11judZJL6fFRUV0d69e3WOsbW1FbelY25WWFgoTnTlC8hXGZzrNQEAAJiSvT4H1apV645LY5VlZGRUayA8s8PLZh07dhQ1nyQjR46kDh06iGXB2/nwww/pvvvuI1dXV1q7di299tprlJOTQ2+88YZ4PDU1lQICAnS+h29zoJOfn0/Xrl2j0tLSKo+5XRkFzsf64IMPqvV3BeMu0cUnX6GjKDsAAABKCJo4n8jUeKaIl8+2bt2qvW/ZsmW0YcMG2r9//x2/l6uRS3gHH+dEzZgxQxs0mcL48eNFDpSEAzBezgPzwg46AABQVND0zDPPmHQQvBtvxYoVtHnzZgoJCdHezwHTyZMnydvbW+f4Rx55hO655x6xI64qnJ/ErV54CY13y/Gy3uXLl3WO4du8duni4iKKdPKlqmNutyTIz8sXUEbQlJyaTaVlGrKz1W9GFAAAwOwNe2uS18N5RxwwcRI3B0jh4eE6j3MC9qFDh0QiuHRhn3/+udgtdzt8HC8pSkFN+/btaf369TrH8G49vp9xO5iWLVvqHMPLhXxbOgaUKay2KznZ21J+cSmdvXrrrksAAACzzjRVxktfvBtt8eLFdPXq1Vse59wgQ5bkFixYQH///beo1cS5R4yz2HkGiGd5qprpCQ0N1QZYXOqAZ4TatWsn6itxMDR16lR6++23tce/8sorYlfcmDFj6LnnnhMBGo+fd9RJeKmNZ9RatWolksl5SZL/rkOHDjX0FIEZ2dvZUqNADzp0IVO0U4nwK2+tAgAAYHQaA7322muaqKgozR9//KFxcXHRzJ07VzN58mRNSEiI5pdffjHoufjHV3WZN2/eHb9nyZIl2turVq3SNG/eXOPu7q5xc3PTxMbGaubMmaMpLS3V+b74+HhxnKOjoyYiIqLKnzFz5kxNaGioOKZNmzaanTt36v13yczMFGPjr2BeY34/qKk3doVmxuokuYcCAAAqY8j7t8F1mniWh5v23nvvvSIniAtBcoXtn3/+mX777Tf6559/yBqhTpN85m87Te8vP0rdo/zp+2dayz0cAABQEZPWaeKSAhEREeI6P7lUYqBTp04ikRtAvh10KDsAAACmY3DQxAETtyVhjRs3FrlBUm7RzbvcAMyhcUXQdPF6PmXmoZ0KAAAoJGjixOiDBw9qd7dxpW5OwOYilKNHjzbFGAHuyMvFgep4u4jraN4LAACK2T3HwZGE24xws14pr6lZs2bGHh+A3u1UeKaJi1y2i6gt93AAAMACGRw03SwsLExcAOTOa/o3MQ2VwQEAQP7lOW5cy1W7K+NddFwvyd/fn1566SVRgRtAzmTwpFQkgwMAgMxBEzfFPXLkiPb24cOH6fnnnxdLdJzbxIng3MQWQO52KiWlZXIPBwAArDlo4tYk3bp1095euHCh6PH23XffiWraX375pXYnHYC51fNxJVdHOyosKaMzaKcCAAByBk3Xrl2jgIAA7e1NmzZRr169tLdbt25N58+fN/4IAfRga2sj2qmwo6jXBAAAcgZNHDBJ9ZmKiorEjjnu9ybJzs4mBwcHU4wRQC+NA6Uil0gGBwAAGYOm3r17i9ylLVu20Pjx48nV1ZXuuece7eOHDh2i+vXrm2CIAPqJDiqfaULQBAAAspYcmDx5Mj388MPUpUsXcnd3px9//JEcHR21j8+dO5d69OhhkkECGNZOBUETAADIGDT5+vqK3nLc0I6DJjs7O53Hf//9d3E/gNztVC5nFVJGbhH5uN0I6gEAAMzeRoU7Ad8cMDEfHx+dmScAc3N3sqdQH1dxPQmzTQAAIHfQBKD0dirsKIImAAAwMgRNYKF5TSg7AAAAxoWgCSwKyg4AAICpIGgCixJdMdN0Ii2HitFOBQAA5Ng9V9nx48cpPj6e0tLSqKxM941p4sSJxhobgMFCarmIhPCcwhI6eSVHO/MEAABg9qCJe829+uqrogRBYGAg2djYaB/j6wiaQO52Ko0DPWjP2WuUlJKNoAkAAOQLmqZMmUIfffQRjR071nijADByMjgHTZzXNCCujtzDAQAAa81p4sa9jz32mGlGA2DEHXQoOwAAALIGTRwwrV271qiDADCmxtoedCg7AAAAMi7PRUZG0oQJE2jnzp3UtGlTcnBw0Hn8jTfeMOLwAAzHOU2capeeU0hXsgvJz8NJ7iEBAIAFsNFoNBpDviE8PPz2T2ZjQ6dOnSJrlJWVJVrMcG8+T08kH8ut6/820un0XPrpuTbUuaGf3MMBAAALeP82eKbp9OnTNRkbgNnaqXDQlJSahaAJAACMAsUtwSJFaSuDI68JAABkLG554cIFWrZsGZ07d46Kiop0Hvvss8+MNDQAY/Sgww46AACQKWhav349PfjggxQREUFJSUkUExNDZ86cIU6NatGihZGGBVAzUcE32qkUlpSSk72d3EMCAABrW54bP348vf3223T48GFydnamP//8k86fP09dunRB/SZQjGAvZ/J0tqeSMo0InAAAAMweNCUmJtKQIUPEdXt7e8rPzyd3d3f68MMP6ZNPPqnxgACMgXdyNtYu0SGvCQAAZAia3NzctHlMQUFBdPLkSe1j6enpRhgSgHFEI68JAADkzGlq164dbd26laKioqh379701ltviaW6v/76SzwGoKSyA4zLDgAAAJg9aOLdcTk55TkiH3zwgbi+aNEiatCgAXbOgUJ30GWLjQq8ZAcAAGC2oIl3zVVeqpszZ061fziAKTUM8CBbG6KM3CJKyy6kAE9nuYcEAAAqhuKWYLGcHewo3NdNXD+KvCYAADDHTJOPjw8dO3aMfH19qVatWndc5sjIyKjpmACMukR38kquSAbv2shf7uEAAIClB02ff/45eXiUJ9V+8cUXph4TgFGDphWHUlB2AAAAzBM0PfPMM1VeB1BL2YEkLM8BAIA5gqasLP3fcDw9y9+kAJS0g+5Uei4VFJeKPCcAAACTBU3e3t56b9cuLS2t1kAATCHA04lquTrQtbxiOn45h5qGeMk9JAAAsOSgKT4+Xnudm/OOGzeOnn32WWrfvr24b8eOHfTjjz/StGnTTDdSgGrgYJ9nm7afvCqSwRE0AQCASYMmbsYr4R5zXMRy0KBB2vsefPBBatq0KX377bfIeQLFaRxYHjSh7AAAAJi1ThPPKrVq1eqW+/m+3bt312gwAKZsp4IedAAAYNagqW7duvTdd9/dcv/3338vHgNQbjuVLNFOBQAAwCxBE9dsmjlzpliOe+GFF8SlWbNm4j5+zBCcA9W6dWtRA8rf358GDBhAycnJVR7Lb3a9evUSOSpLly7VeezcuXPUp08fcnV1Fc8zevRoKikp0Tlm48aN1KJFC3JycqLIyEiaP3/+LT9j9uzZFBYWRs7OztS2bVvMnFmIBgHuZG9rQ1kFJZSSWSD3cAAAwFqCpt69e4vq4P369RPVv/nC1/k+fswQmzZtomHDhtHOnTtp3bp1VFxcTD169KDc3NxbjuWimlXt4OPdehwwFRUV0fbt20VCOgdEEydO1B5z+vRpcUzXrl3pwIEDNGLECBHsrVmzRnsMNx0eNWoUTZo0ifbt20exsbHUs2dPSktLM/QUgcI42dtRfT93cR1LdAAAUF02GgWtV1y5ckXMFHEw1blzZ+39HOj07duX9uzZQ0FBQbRkyRIxK8VWrVolHrt06RIFBASI+7iJ8NixY8XzOTo6iusrV66khIQE7XMOHDiQrl+/TqtXrxa3eWaJZ71mzZolbpeVlYnlxtdff13sFtSnlpWXlxdlZmaiVpUCjVi4n5YeuERv92hIw+9rIPdwVC2/iOtd2epdhgTUi2ubOdnjd11T1/OKKKdQd/VDaextbUWJFmv8XWcZ8P6t1+65m23ZsoW++eYbOnXqFP3+++9Up04d+vnnnyk8PJw6depU3XGLAUu97iR5eXk0ePBgsXQWGBhYZWI6LxVKARPjGaJXX32Vjhw5QnFxceKY7t2763wfH8MzToxnqfbu3Uvjx4/XPm5rayu+h7+3KoWFheJSnQKgIE9eEwdNCRfxe6qJPWcyaOC3O2lw21D6sH+M3MMBE9p56io9+f0uGtY1kkbd31Du4ajW5mNX6Jl5u0k50xO3N6R9Pbyujb089+eff4qAw8XFRSxjSYEDBzxTp06l6uKZHQ5iOnbsSDExN35pI0eOpA4dOlD//v2r/L7U1FSdgIlJt/mxOx3DgU5+fj6lp6eLZb6qjpGe42acj8WRqXRBEryytQkvD8Q3HbtCuQr/xKdk87efoZIyDf204yztOnVV7uGACc3bdppKyzQ0f9tpKixB0eLq+nH7GREwcV4lz9op9cL4db3yUIrcp0zRDJ5pmjJlilj+GjJkCC1cuFB7Pwc7/Fh1cW4TL59t3bpVe9+yZctow4YNtH//flIanpXiHCgJB2AInJSreV1vCvd1o9PpubQ6IZUeaRki95BUJzO/mNYevay9/d7SBPrnzXvIwc7gz16gcNdyi2hDUnk+J2+g2JCYRr2aBsk9LNVJzymkjceuiOurR3SmSP/y3Eol+mR1En298SSN++sQNQvxoro+rnIPSZEM/t+Od7dVzjeS8GwL5whVx/Dhw2nFihWi8nhIyI03Mw6YTp48Kdq42Nvbiwt75JFH6N577xXXecnu8uUb/5Ez6ba0nHe7Y3jtkmfMfH19yc7OrspjqloSZLwLj7+/8gWUi9fpH4qrI67/tf+C3MNRpVWHU6iopIzCaruSj5sjHU/LoblbT8s9LDCBFYcuUXHpjfWkP/ddlHU8arXswCUxWxcb4qXogInxEmxcqDdlF5TQGwv3U3FpmdxDsoygiYOIEydO3HI/zxBFREQY9Fycg84BEyd2c4DEOVGVcQL2oUOHRCK4dGFc2mDevHniOrdyOXz4sM4uN96Jx0FMdHS09pj169frPDcfI7WB4WTxli1b6hzDy4V8WzoG1E8Kmrg6eEpmvtzDUZ2/Kt44n2gdSuN7NRbXv/j3OF26jnNpaaQgaVCb8tnzjclpdDXnRg4n6Ef6gPZwC+XPbPOM8ZcD48jD2Z72n7tOn687JveQLCNoevHFF+nNN9+kXbt2iU/vvGvt119/pbffflskXxu6JPfLL7/QggULRK0mzh/iC+cZSQEa5zdVvrDQ0FBtgMUlCjg4evrpp+ngwYOijMB7770nnptng9grr7wiktbHjBlDSUlJ9NVXX9HixYtFvpSEl9q4aCeXLEhMTBR/Fy59MHToUENPESgUTzdzbhPnFyzdf0nu4ajK+Yw82n0mg3hjzYC4YHq0ZQi1CfOh/OJS+nD5UbmHB0Z06koOHTh/nexsbWjU/Y2oaR0vkce2ArkuBjl2OVtsPOFcpn6xwaSW/yM/friZuP71ppO09Xi63ENSf9DEsz+8m61bt26Uk5Mjluq45tHLL78stucb4uuvvxYJ5LzUxqUEpAvXTNIXL6vx0h5/5Vmhp556SuRbcY88CQdYXHKAZ5e4/tKnn34qKphzQrvkiSeeoP/973+ivlPz5s3FrBaXI7g5ORzU7WFpiW7fBVQHN8CS/eUzDx3q16YgLxfxgWnygBjxxrr6SCrFV+S/gOX8rjs38CU/Dyd6uMWN1wwYPjN7byN/sZytFn2aBdGgNqHiw+XIxQdEXhYYoU4Tb9PnZToOnHimx91d2eu1poY6TeqQVVBMrab8K3Jzlg/vRE1DvOQekuLxfxFd/7eRzlzNo08fi9VJop/6TyJ9u/kUhfq40tqRncnZwU7WsULNlJVp6J7p8XTxej7NHBQnZkj4TbPd1PVitunfUV0Un5ujBJzH1PHjDZSaVUBfP9lCdUn0XIut/+ytdOxyDnVp6Efznm1NtraWW7/JkPfvam974TwgDpbatGlj9QETqIenswP1iC6fPURCuH72n78uAiYXBzt6IEZ3Y8Sb3RpQoKczncvIo6/ib811BHX570yGCJg8nOzp/orXia+7k3jjZEvwmtG7xhUHTJ7O9nRflD+pjYujHc0c1EKUIuAyLd9vPSX3kNRXcuC5557T67i5c+fWZDwAJsfLDZyfwTtb3ukdhS3zdyEty3DA5Oak+18G357UL5pe/XUfzdl0igbE1aGIipY1oN4lpd5Ng3RmDTmReX1SGi3Zd5Heur+RRc86GMOfFa+ZvrHBoo2TGjUK9KCJ/aLp3SUJNH11MrUNr02xdb3J2un9bsH93LgkAJcVuHbt2m0vAEp3TwM/8nV3pKu5RaJaL9weFzVcfrA8AVjKbbkZB1M8E1FUWkYT/z6CXDEVt0xZebjq33W3KH+xq+pSZgHtPI2ipneSV1QiasGxR27zmlGLwW1CqXfTQLE0+/pv+ym7oJisnd5BE+8m4/U+bn7LjW9/+OEHUSrg5guA0vHM0oOxUs0m1J+5k/ikK6KoJfek6lDft8pjOCn8w/5NyNHelraeSNe+8YK6cOFS7o9Wx9uFWofdaGXFeNapb7PyvByebYLbW3MklfKKSqlebVdqEVqL1Ixf29Mebib+TfAS/LtLEqz+Q5HeQRP3fktJSRHb9pcvXy6qXz/++ONii7+1n0RQH+mT9Lqjl0VQAHdemuNlN94pdzv1arvRsHsjxXUuQYBPpOqzpOJ3za+NqpbfpFpD/xxOEYnCcOclTq4LZwnNb71cHOjLQXHi9b/s4CX6fa9157UZlMzBdY8GDRoktu4fPXqUmjRpQq+99hqFhYWJXXQAatEk2JMaBriLXXT8JgC3ysgtovjk8lICD8fdvTjfy10iRLXwtOxCUfQS1CMtu4A2V9TkkYrA3qxVvVpU18eFcotKae3RqntyWrvUzAIx26rva0YtWtarpW3aPOnvI3QiLZusVbUzYG1tbUUUzbNM3OwWQE343670yRnLDXdupcEBJieF3g0v4Ugd0rmx79FLWWYYJRiz3Qe30bhdIn95K6Ly1wzaqlTt7wMXRX2j1mG1KLS2ZfVue7VLfeoYWVsUtB2+YL/IgbNGBgVNhYWF9Ntvv9H9999PDRs2FO1LZs2aRefOnUPZAVCdAc15+pxEpetzV/PkHo5ilxkMaQHRuaEf9WkaJN6A31t6WNT9AfUUtJSKv96O9PjW41coLavALGNTC55AkHbNqaFtiqF4yfbzx5tTbTdHSkrNpmn/JJI10jto4mU4rtb98ccfU9++fen8+fP0+++/U+/evcWsE4DaBHo5U8eK5GbpTQPKnazUSuNBA1tATOgbTW6OdrTv3HX6fe95k40RjCMpNYuOXMoiBzsb6tvszr/rMF83sVTDsfDfB9CKqDI+h1wMkjdEcMkGS+Tv6UyfPh4rrv+446xIerc2ekc7c+bMEZUyuSnvpk2b6KWXXqKHH374lguAGhPCuWgfNjTcIC1ZSq00DA1GR1bkP0xblSRyo0D5v+v7GvtTLT3afUivGWlWBXRnZu+PChDJ05bq3kb+9OI95b1fx/xxyOoadusdNHE/Ny414O3tLcqN3+4CoCY9mwSSq6OdqHjNMyNQ3kpDu1xTzWWGZzuEUeNAD7qeV0zTVycZeYRgLLyMuvSAtNtLv99136bB5GhnK5ZokLdWrqS0jJYdlF4z6q7NpI/RPRtTsxAvsfP4zYX7xd/fWtgbUtwSwNJwResHmgSKek28vZ6XHqzd7ipaaRjK3s6WpgyIoUfn7KCF/52nx1rVxblVoO0n0+lyViF5uzpQ18blrVLuxsvVQRS7XJWQKmZoo4OjydptOZ5O6TlFIt+H8/osnaO9rehN2OfLrfTfmWv05YYT2t11lg7JSGD1pNkUbq3CFbCtnVSb6eZWGoZqFeZDj7cqP7fvLU2wqk+jaltS6tfMsHYf0mtm6YFL+L1WWqrkBsfW0papXm03+uih8t2yszYcpx0nraNSvHX8dgHuoH392qLpLE81xyeV1yWyVryN+J/DqUZbZhjXK0rMYiSmZInEUVCO3MIb7T4eMvB3zW1zark60JXsQtpmJW+Wt5NVUCyK5LJHLHDX3J30b16HHmsZIjYGjFi03yryFxE0gdXjHWL948p3DVl7/RmplUZIrVtbaVSHj5sjjX2gsbj+2dpkUfwPlIEDJq65E+7rRnEGNmLl5RlpV6U0M2mtVh3mGeoyivR3p5g6nmRtPujfhCL83MQy7+jfD1r8hhoETQCVqvfyTJM1fFq6HekNkKtCG6uT/ROt6oqiiVxJesrKo0Z5Tqi5v/ZX1BSqZrsPaYmOt51bc9sc6YMWz8xaQtsUQ7k62ov8Jg6k1yel0bxtZ8iSIWgCIBIVr/lTInfz5krYVttK49iVO7bSqA4OvjgpnGMwzhvbcrz8Z4B8UjLzaXvFshr3FawO3j3FMwwFxWUiKdwanc/Io92nM0SRXC6Wa62aBHvRu72jxPWPVyVRwsVMslQImgBumm2y1iU6bqXBuQl3aqVRk/9Un+kQJq5P/PsIEu5ltnT/JdHuo024D9X1qV67D55VecTKWxEtrSjN0aF+bQr2diFrNqR9PbHbtqi0jF7/bb9Y5rdECJoAKjzYPFjkNx08f11UxLbatilGnGWqjLck+3s40en0XPp20ymT/Ay4O845kZZhH6lhsr80S7Xj1FW6cC3P+s7jfsNqXFkyGxsbmvFoMwrychav8Yl/J5AlQtAEUMHX3UnsCrLGT87cSuNoin6tNKrLw9mB3utbXtNnVvwJ9PuTScLFLDqelkNO9rbUq4btPup4u1C7iPINA9bWVoXbDHFw4OJgRw/EBMo9HEXwdnWk/xsYJ5bi+UMY1/GyNAiaAKpsq3LRqprNGtpKo7r6NQsSndJ5t9GkZQkWv9NGyQngvJTi6Vzzdh9SQjjPXlnT71OameWAyd1J7zrRFq9NuA+92a280OV7SxJEYGlJEDQBVNI9KkBUwuaK2LtOZ5C1tNJYYqZlBp7Cn9w/RrThiE++QmuOlNe3AfMo5nYfFTNCxqop1CsmkJwdbOnklVw6dMFyE4Ar45y85RUbRoy5acJSDL8vktqG+4gds6//ts+ichgRNAFUwhWw+zQrX7KwxKnlqmw7kU5p2Ya10qgJTjJ/uUuEuP7h8iOiyCKYB++OvJpbRL7ujnRPA1+jLbtyD0drqtkUn3RF9FXkHL2OkcY5j5bEztaGvhjYXBRA5eXg6auTyVIgaAK4zXIDV8bOL7KcT0i3I80yGdpKoyaGdY2kuj4udCmzgL7ccNwsPxNuLCk9GFtH9Ac0Fmm2ZfmhFCoqsfy2KtIHKv57c4AAtwrycqEZj8aK6z9sPU0bkixjVhlBE8BNWtWrJSpi85bZtUctu/5MTg1aadR0Ru/9fk3E9R+2nKZjl7PN9rOtFbcJWpd42WgtcirrFOlLfh5OojDspopaX5bqWm4Rbahot2TO14wadY8OoGcrSo28/fshupyl/o4ACJoAqijGKG27lz6ZW6qatNKoqW5RAdQjOkAUFOWGvtaURCyHfw6XzwI1CvCgJsHGbffBs1YDmltHWxUufltcqqHoIE9qHGh9bVMMNb53Y/HvjQPqEQsPiBxKNUPQBFCFhyqW6Lh6NVfKtlTSG1x1W2nU1MR+0WLLNldVtvQAVTG/axO1+5A2EaxPTKPMPMttqyLVZjL2bJ2lcrK3E21WXB3tRD2vr+JPkJohaAKoAs+8tAj1FhWypd1GlubS9Xzxn1hNWmnUVEgtV3qjWwNxfeo/iRb9Zisnron135lrot0Hd6Y3hehgnnnxEBWhVxy2zNfMqSs5tP/cdVGHiIvhgv6bP3jXLPti/XH674x6dyYjaAK4y2yTpbZVWXrgYo1baRjD853CqYG/u9jVNWNtkmzjsIZkf849CvRyNtnPkcoYWOqsoXQeOzf0I38P051HS/RIyxCROM/Lc2/+tp+u56mzMTqCJoA7FGLkCtmJKVniYnmtNC4apZVGTXF39MkDyj+F/rrrnGhjA8Zu93Fjac6U+jcPFrMwe89eozMWVtSQi91KQZO0wxYMw6/zsNquYtfs2D8PqTKPEUETwB1aAnRrHCCuS/9ZWgqunXLCSK00jKFdRG2RV8X/h3JSuNqTRZVk37lrdPZqnsgpkeopmYq/pzN1auBnka8ZXlK6cC1fFL/lDQxgOK6cPnNQC/FhlAvb/rLrHKkNgiaAO5C2FHM3c0t6I/9zn3FbaRjD+N5R5OFsT4cvZtKCXWflHo5FtvtwdTR9uw9p5pJnt9Q4k3C389irKVdAN089M0vUNMSLxj7QWFyfvOKo6mbxETQB3EHXRv6iUjZXzObK2ZbSSmP5QeO20jAGrvMzpmcjcX36mmSL3rVo1nYfZv5d94gOJDdHOzqfkU97zl4jS1BQXCpKNjAszRknj5H7XHIJjNd/2095RerpCoCgCeAu+TYPxlpW/RlTtNIwlsFt61GzEC/KLiihaf8gKbymNiSmUVZBCQV6OoslUHNwcbTTLvlaSkL4uqOXKbuwhOp4u1CbMB+5h6N6NjY2NOPRZqINDacJfLDsKKkFgiYAPVtErD6SKipoq52pWmkYA7ekmDIgRmyN55yYHSfLSyJAzWoKDTBzuw8p4ZwLQfIsjdpJH5j4/wIufgs1V9vdSfSn49f6oj3ntTOiSqes/zEBFKh5XW+K8HWjguIybcsRteI6SKZqpWEszUK86am29cT1CX8nWEUvM1PgCszxFe0+zP27bhdem4K9nMWMIRe7VLMr2YW0+Xj50jzaphhXh/q+NLxrpLj+zl+HRT0xpUPQBKDHVLL0pqP2JbqVJmylYUxv92gklg956p6bfYLh+JM7t6hpWseLGgZ4mPVn82yMVDBV7a+ZZQcviU0g/OGpvp+73MOxOG92ayD6ffLy5+sL94ucSyVD0ASgB6mKMlfQvng9n9Tend1UrTSMxcvVgd7pHSWuf7n+OF24pvxPoEpdmpOWl81N+qDBDXzTcwrJEtrPgPFxisD/DYojT2d7UaPtf2uTSckQNAHogStmtw33EXWEuPyAGp29mmvyVhrGxG/2XK2cGwp/sFw9iaJKwDN0/AbEeUxytfuI9Peg2BAvMdullnyVmyWnZtORS1mirlDfZmibYiqcYD/90Vhx/ZtNp8RmFaVC0ASgJ2nLNicoq7H+jLlaaRgLz4RxUri9rY3YvfTv0fJcLNB/RvHehn7k6+4k2zikWS617qKTKqlz6REfN0e5h2PRHogJpKfahYrroxYfUGzJEQRNAHrionZcQZs/xXMBRjXhIO9GCwjlzzJJOBfnhXsixPX3lx+h/CL178QyR7uPpfsvKSJxuV9ssAh6+fVy/HI2qQnnMUmzyqjNZB7v9YkWTZ/Tc4rorcUHxb9lpUHQBKAnD2cH6lHRhkJtn5zN2UrD2N7oFil2YnELi9nxJ+QejuLtOp0h8u64unr3qADZt5Xf28hfJ8dKLbafTKfLWYXk5eJAXRuXt4YB0+JK67MGx5Gzgy1tOZ5O32w+RUqDoAnAANIsDe+oUfouj8r+NHMrDWPi8U56sIm4/s3mk3TySo7cQ1JF4nLfZkGKaPchvWZ41kaJMwe3s6TiNdMvNoic7OU/j9Yi0t+DPqh4vX+6Npn2n1NWVXkETQAGuCfSV+SIcA2cTcnKTVa8uZXGCgW2TTEEN0jltgvFpRqa+HeCKnPKzIGXL6V2Hw/FKeN3zb833hmVkllAO0+po1hpbmEJraqoyYalOfN7vFVdEfTzJgJus5KZX0xKgaAJwMDtsf0rdiNJSaJKJ0crDVMkhb/fr4nIKdt24iotP1QeGICutUdTKbeolOr6uIjaN0rAs119K1oRSTOeSsdFbHnXZrivG8XV9ZZ7OFbHxsaGpj7cVPw75mX5d5YcVswHJVmDpmnTplHr1q3Jw8OD/P39acCAAZScrFuj4eWXX6b69euTi4sL+fn5Uf/+/SkpKemWE3zzZeHChTrHbNy4kVq0aEFOTk4UGRlJ8+fPv2U8s2fPprCwMHJ2dqa2bdvS7t27TfQ3BzWTlhv+TUwTFbaVTnqjMncrDWMLre2qrR7M3dGzCpR/7s1NyrXjWSYltft4uGIX3aqEFFU0Z5U2TfDuPyXXM7Nkns4O9OXAOLGRYOWhFFr033kiaw+aNm3aRMOGDaOdO3fSunXrqLi4mHr06EG5ubnaY1q2bEnz5s2jxMREWrNmjYg2+ZjSUt1dNHxMSkqK9sIBmOT06dPUp08f6tq1Kx04cIBGjBhBL7zwgng+yaJFi2jUqFE0adIk2rdvH8XGxlLPnj0pLU3dLQDA+KKDPMUOD66szRW2lexqTiFtTJanlYYpvNQlQrS04dYWn609JvdwFCUtq4C2HL+iE6QoRct6tSjUx5XyikppzRFltyJKycynbScr2qYo7Dxam7jQWvR2z0ba3bNK2IEpa9C0evVqevbZZ6lJkyYiSOHZn3PnztHevXu1x7z00kvUuXNnMQPEM0VTpkyh8+fP05kzZ3Sey9vbmwIDA7UXni2SzJkzh8LDw+nTTz+lqKgoGj58OD366KP0+eefa4/57LPP6MUXX6ShQ4dSdHS0+B5XV1eaO3eumc4GqAV/8rxRf0bZS3QrDqWIvICYOp5mb6VhCpyQ+2H/GHH9px1nKEFlpR9MiTcncJ51i1BvCvN1I+W2IlL2Eh2Xa+CVoDZhPqKoLcjrpXsi6J4GvqL35/AF+2VvAK2onKbMzPL/AH18fKp8nGegeEaJA6C6devqPMYzVr6+vtSmTRsR6FRe/9yxYwd1795d53ieReL7WVFRkQjUKh9ja2srbkvH3KywsJCysrJ0LmA9eKmLVz/2nOWt/DdmRhXbAkIhScHG0KmBr6j/wwHCe0sTVLUjyxzLsEpNXJY+aGw7kU6pmcosXMjvG2iboiy2tjb02ePNxQac5MvZNGWlvN0BFBM0lZWViWWzjh07UkxM+SdJyVdffUXu7u7ismrVKrGU5+h4ozrrhx9+SIsXLxb3P/LII/Taa6/RzJkztY+npqZSQIBuvRK+zYFOfn4+paeni+W+qo7h771dPpaXl5f2cnMQB5YtwNOZOkb66uQ/KLKVxoVMWVtpmMp7faLI3cmeDpy/Tov2KCPXQU5HL2VRYkoWOdrZil1HSlSvtptITucY9+8DynzNcMuU42k55GhvS70Veh6tkZ+HE332eKxoAeVoZyfrByXFBE08U5SQkHBLAjd78sknaf/+/SIHqmHDhvT4449TQcGNTyoTJkwQwVZcXByNHTuWxowZQzNmzDDpeMePHy9mxqQLLxmCdZG27/Nyg1J2diixlYapgtZR9zcU1z9elSRyt6yZ9Lvm7f3erspt9/Gwwl8zf1bMMnGJC05EBuXo3NCP/h3VhSb2i5Z1k4MigibOMVqxYgXFx8dTSMitU8s8k9OgQQOR2/THH3+I3XNLliy57fPxzrcLFy6IJTTGOU6XL+v2reLbnp6eYlceL+vZ2dlVeQx/b1V4Fx5/f+ULWJceTQJEhe1zGXm096yyCrDxJzGpOJ/crTRMZUj7eiIpn2u4cOBkrUpKy2jpgUuqWFLq0zRIzOLwMsvRFGWlNHCx2mUqOY/Wqr6fu9xDkDdo4k8aHDBxALRhwwaRq6TP9/BFCoiqwjvkatWqJQIb1r59e1q/fr3OMbyUx/czXurjXXqVj+HlQr4tHQNQVaXqXjFBimwRsfP0VbqUWaCIVhqmrJk15aHypfzf916g/85kkDXadvKq2E1Yy9VB27JEqbxcHah7lL8iE8J55+HV3CLydXekexqgbQooMGjiJblffvmFFixYIGo1cf4QXzjPiJ06dUrkDnGSNu+q2759Oz322GNidqh3797imOXLl9P3338vlvZOnDhBX3/9NU2dOpVef/117c955ZVXxHPxsh3PUnGOFOdAjRw5UnsMlxv47rvv6McffxTlDV599VWReM676QBu55GKT6RccVvuXR2VSbNMSmmlYSotQmvRoDbl+YTvLUlQVWsbY5ESlzk5nmdxlE7alPD3gUtilkxpifQPxtYhBzvln0eQh6z/MjjA4Xyge++9l4KCgrQXrpnEuGzAli1bRIDEBSmfeOIJEVxx8MTFMJmDg4MoSskzQs2bN6dvvvlGlA/geksSnsFauXKlmF3i0gZceoADLd5BJ+Hn/t///kcTJ04Uz8OzVVwS4ebkcIDKuMJ2kJezqLi9IUkZNb2U2ErDlMb0bCxmWXjJ58ftuqVILF1OYYm27pFSd83drEsjP/Jxc6T0nELacqK8HpLceIl33dHy9AwszcGdyNq5826JgMHBwfTPP//c8ZgHHnhAXO6GAzNOJr8TXirkC4C+OCGRyw98vfGkWG7o3VT+HTdKbKVhSrXcHGl8ryga8+ch+nzdMerTLIiCvFzIGqw6nCLq10T4uVFsiBepAc/iPBgbTPO3nxGvma4KWFLk88jFahsGuFOTYOSnwu1hDhKghqTqy1x5Wwm7uP5UaCsNU3q0ZYioOs3B4pQViWQtpLwg3smppnYf0mzO2iOpimiHI53Hh1V2HsH8EDQB1FCDAA9qWsdLVN5efrB8942crTS2VrTSsKYWEBwcThkQI2pScWubTcfKz4Elu3g9n3acuiquS02k1YJfL5H+7lRYUkarD8vbVuV8Rh7tPpMhagCp7TyC+SFoAjDiJ2e5C11ycq3USoM7tFuTqCBPerZDmLg+6e8ERSXmm8LSin9r7SJ8KKSWq2pbEUm1keQivWY71ve1mmVdqD4ETQBGwDuXuBs3V+A+kSZfU0npDUgtScHGNvL+hhTg6URnruaJ/CZLpdvuQ52/a84F5NmdXaczxGyPHNA2BQyFoAnACLji9r2N/GStP8OtNJJSsxXdSsPUuLXK+/2aiOvfbD5FP++wzN10hy5k0skrueRkb0u9YqouwKt0dbxdqH1EbXFdrrYq+85dFwG2i4Md9WyizvMI5oWgCcBIpO39vGwiR28ktbTSMLVeTYNoRPcG4vrEZUdkzzMz5ZISv9F7qLjdh7REJ1dbFek1w4Gnm5Osm8lBJRA0ARhJtyh/UYGbK3FzRW5zUlMrDXN4s1sDerpdPeL34VGLD4hqz5aCt8YvO2gZv2sOcJ0dbOlUeq5ovmxOhSWltPxgiqqXOMH8EDQBGAlX3u7bLFiWJTo1tdIwV6Lx+w82ETWbiks19PLPe+mgmd+UTYV3BmaIdh9O1CnSl9S+nPpAxbKYuTdRxCeliaKWgZ7O1L5++TIhwN0gaAIwIumTPxfL48rc5qK2VhrmwOUHPns8VgQWeUWl9Oy83XQiLYfUTlpSGtA8WPTfUztplodnz3gWzdz1zPrHBYt/KwD6UP8rDkBBuAI3V+LmIotcmdscsguKVddKw1yc7O1oztMtRbXsa3nFNOSHXZSSWd7bUo0y84rp36NpFvW77hjpS/4eTnQ9r5jik83Tiohn6rgYbeVeeAD6QNAEYORlIek/YemTrKmtSkhVXSsNcy8BzX22tTg/nG/29A+76VpuEanRisOXqKi0jBoHelC0hbT74FkeLj9QecbU1FYcuiSWbWPqeFKjQA+z/EywDAiaAEy0I4grc3OFblNbIrWAEHVvsMxQldruTvTTc21E/gov0T3343+UV1RCanOj3Ye6E8BvJv19uOn19bwis7YaAjAEgiYAIwvzdRN90LjqAFfoNqUL1/K0rTSkT+tQNa6a/dPzbcjLxYH2n7tOr/6yz6w5NDV19mou7T17jTj9pn9zy/pdNw70FBXdefZn+aHyHW2mcvJKjtgUwDNc3DgYwBAImgBM+MnZ1C0ipKBMja005NAwwEMs1XExQ96FNvqPg7LU1KrJLFOnBn4U4OlMluaRFuZZopNmZrs09CM/DyeT/iywPAiaAEygb9NgUZmbK3RzpW6Tt4DAMoPeeBbwq6daiLY3HHR+uOKoLIUVDcHjk7bk8zKsJXqwebCYReNZwNPpuSb5GRwgS+fRmhpag/EgaAIwAS9XB1HssvIWcZO20miKFhCG6NrIn/73WKy4Pn/7Gfpq40lSMl6WO5eRR26OdtSjSQBZIn8PZ7qnQXkroiUmmm3afSaDLl7PJw8ne7o/2jLPI5gWgiYAE5G2hHOlbq7YbWzSLJPaW2nIhXPAJvaNFtdnrEmm33afI6WSEpe5graro73FL2v/ZaJWRNJrhouecjFaAEMhaAIwEc6Z8HFzFJW6t55IN+pzcwKzlDBraTupzOm5TuE0vGukuP7uksO0OsG0ScjVUVBcKrbIW/LSnKRHdKAoEXHhWj7tOXvNqM/NxWb/OYx6ZlAzCJoATIQrc/drFmSSFhGW1EpDbm/1aEiD2tQVux3f+O0AbT9p3AC3pngbfnZBCQV7OVO7CMtu9+HiaCea55oiIXxd4mXKKSyhkFouoggtQHUgaAIwIekTLVfs5srdxiK9oVhKKw05cW2rKQOaih5oXDjypZ/2UsLFTFIK7e86rg7ZWkG7D+k1s/JQiphlM5Ybmyas4zyCaeB/WwATahbiRfX93ETFbq7cbaxWGusTLauVhty4Zs8XA5uL0g08G/HM3N0m28FliPScQtqYfMWqlmHbhvtQHW8Xyi4soXVHLxvlOdOyC2jzsfLz+BBeM1ADCJoATN1WpeI/aak+TE1ZYisNJeDE4O+GtKImwZ50NbeInv5hF102Q0X3O1l+8BKVlGlE8B3pbx3tPngWSCoHYKxl7WUHLonl17hQbwr3dTPKc4J1QtAEYGJSpW6u3M0VvGvKUltpKAHvQpw/tA2F1XYVychDftgtZvbkYum1mW7noYp/25y7xxspjPeawSwT1AyCJgAT46WG9hUJvDVtq3Im3XJbaSgFV4n++fm24mvy5Wx6/sf/xM4rczt+OVvU4uIinP2srN1HfT93iq3rTaVlGlp2sGavmcSULDqakkUOdjbajRkA1YWgCcCMn5y5rUpNqk9LMw8dI30tspWGUtT1cRUNfj2c7cXW9+EL9lGxCWpt3QnXKmL3NvITDYetjdRWpabFYaXXzH2N/cnb1dEoYwPrhaAJwAx4G7Wzgy2dupIrZg+q3Tal4g3kESwzmBw3kP3hmdai4vr6pDQa9+dhs/Wp45+zVFqas9Lfdd9mwWKWLeFiFiWnZlfrOXimytrPIxgXgiYAM+XKcOXumtSf4RmP8xn5Ft1KQ2nahPvQ7MEtxO46niX8eHWSWX7uzlNXKSWzgDyd7cUMiTXiwrBdK/7u0ocFQ207kU5p2YXk7eogWucA1BSCJgAzkT7pco4GV/SubjKrpbfSUJru0QH0ySPNxPVvN5+ibzadNFvblD7Ngq263Ye0RPf3/kti1shQ0geUB2ODRbFZgJrCvyIAM+lYv7ZILr6WVyx2BRnCmlppKNGjLUPond6NxfVpq5Jo8Z7zJvtZeUUltKqinYsUNFgrnmnycnGg1KwC2nHyqkHfy/W21hwpr/MklTAAqCkETQBmwpW7uYJ3dZbouJiltbTSUKqXOtenlztHiOvj/zpstMKLN1t75DLlFZVSqI8rtbTydh9O9nbUt2LHm6GvmdUJqZRfXEoRvm7UvK63iUYI1gZBE4AMS3QcBBlS/8faWmko1bhejemxliFiqYh31O06Zdjshz44d0qqw8XFUa2d9Jrhivq5hSWGt03BeQQjQtAEYOYdWVzJmyt6c2VvfVtpSMt5KGgpL37znfZwU+oeFUCFJWX0wk976OilLKM9P1cg5+RlhiWlci1CvUWxUZ414h6O+rh0PV8Uk2WoZwbGhKAJwMykcgFSYvfdWGMrDaUvs84aHEetw2qJJdNn5u2mc1drXumd/X3gomj30apeLapXG+0+pED1oTjDXjNLD1wkLofGfey45haAsSBoAjCz/s2DRUVvruzNFb71bgGBmQfF4B1t3z/TWswacpuPp+fuqnG7D67D9ede1BSqijTrtu1kOqVk5t+9nlnFawb1zMDYEDQBmJm/pzN1auCnV0NSbqVx+KJ1ttJQOt7VxVXD6/q40NmrefTM3N2UVVD9PnXc6oPbtjja2VKfpmj3UVlobVcxs8ezR3drRcTFME+k5YiipL2altdGAzAWBE0AMpC2knPRvju1VbH2VhpqCIB/fq4t+bo7iqDnxR/3iPIQ1bGkYnake7Q/ebk6GHmk6ifNvv25986vGSmRvkeTQFFUFsCYEDQByKBHdKCo7M0VvrnSd1XQSkMdwnzdaP7QNuTuZE+7TmfQG7/tpxID+9Tx8UsrZlAersjfAV29mwaJApXH03LoyG2S77k/oNTgF5smwBQQNAHIwMXRTlT2vlP9GamVhocVt9JQi5g6XvTdkFZiaW3t0cv03tIEgxozbzmRLnZJcuuQLo3Kl27h1uXQ+6MD7pgQvin5CmXkFpGvuxPdE+lr5hGCNUDQBCAT6ZPwikMpVS7pSK00+lp5Kw21aF+/Nn05KE4k+S/87zzNWJOs9/dKQQC3+3Cww3/LtyNthlh28KKYVbqZlCPImy14lyOAseFfFYBM2oXXFhW+eds6F7usDK001OmBmECa+lBTcf2rjSfp+y2n7vo92QXFtLai/hCWlO6sc0M/qu3mSOk5RbTluG4rIi4Wuy6xvEo7ziOYCoImAJlwZe+HpITwm5bouIgfWmmo08A2oTS6ZyNxfcrKRFqy/87tP1YdThWFMiP93alpHS8zjVKdeBbuwYpWRNJMrGTl4RTRCJvLQEQHeco0QrB0CJoAZCQV7eOK35zTcvNyDdenQQsI9Xnt3vr0XMdwcX3074coPkl3JrGq3V74XetHSpTn3n+Z+TdKPEjBKc4jmBKCJgAZ8exCbIiXqPjNlb9ZauaNVhpYZlAnftN+r0+UeAPn3+2rv+6lvWczbjnuwrU8seOO3+O5ryDcXUwdT2rg7y5mlVYdLl/CPns1l/47c03kk+E8gikhaAKQmVROQJpdQisNy1l+nf5oM1Fjq6C4jIbO+4+SU7N1jpFKSrSPqE11vF1kGqn6AtKbXzNSAnjHSF8K8HSWdXxg2RA0AciMK31zxW+u/M0VwLVLc5hlsogcnK+ebCGazmYVlNCQubvE7NLN7T7QnNcwA+KCxezc7jMZou+fFDRhZhYsOmiaNm0atW7dmjw8PMjf358GDBhAycm623Rffvllql+/Prm4uJCfnx/179+fkpKSdI45d+4c9enTh1xdXcXzjB49mkpKSnSO2bhxI7Vo0YKcnJwoMjKS5s+ff8t4Zs+eTWFhYeTs7Ext27al3bt3m+hvDnAD1+a5t1F5HaaP/knUttLo2xRtUyyBq6M9zX22tVhSupxVSEN+2E1Xcwrp4IVMOpWeS84O3O4DbVMMEeTlQh3q1xbXJy5LEG1sXB3tqGcTtE0BCw6aNm3aRMOGDaOdO3fSunXrqLi4mHr06EG5uTeamLZs2ZLmzZtHiYmJtGbNGvHpjI8pLS2va8NfOWAqKiqi7du3048//igCookTJ2qf4/Tp0+KYrl270oEDB2jEiBH0wgsviOeTLFq0iEaNGkWTJk2iffv2UWxsLPXs2ZPS0m6fwAlgLFJZgY3J5duo0UrDsni7OtJPz7cRS3AcKA2d/x/9vOOseOyBJoGimjhULyFces30igkSASqAKdloDClba2JXrlwRM0UcTHXu3LnKYw4dOiQCmhMnTogZqFWrVlHfvn3p0qVLFBBQXi12zpw5NHbsWPF8jo6O4vrKlSspISFB+zwDBw6k69ev0+rVq8VtnlniWa9Zs2aJ22VlZVS3bl16/fXXady4cXcde1ZWFnl5eVFmZiZ5emK7KximsKSUWk/5VyzhMK4uLVU/Bstx8koOPTZnh6haLfnxuTbUpSGqgBsqt7CEWk35l/IrCsP++kJbkdMEYChD3r8VldPEA2Y+Pj5VPs4zUDzrFB4eLgIatmPHDmratKk2YGI8Q8Qn4ciRI9pjunfvrvNcfAzfz3iWau/evTrH2NraitvSMTcrLCwUP6PyBaC6nOztqG9s+XKcaKWBN1GLVN/PneYPbS36DjJ/DyfqWLHMBIZxc7IXxURZkJcztYvAeQTTU0zQxDM7vGzWsWNHiomJ0Xnsq6++Ind3d3HhmSVeyuMZJJaamqoTMDHpNj92p2M40MnPz6f09HSxzFfVMdJzVJWPxZGpdJGCOIDqer5TOEX4udGb3RqIxqRgmZqFeNO3Q1qJwqWvd2uAdh818HKXCAqr7UojuzckO643AGBiilkA5twmXj7bunXrLY89+eSTdP/991NKSgr973//o8cff5y2bdsmErblMn78eJEDJeEADIET1HQWYsNb98o9DDADXkbaPKar3MNQvcaBnrRxNM4jWFnQNHz4cFqxYgVt3ryZQkLKk/sqk2ZzGjRoQO3ataNatWrRkiVLaNCgQRQYGHjLLrfLl8v7D/Fj0lfpvsrH8Nol78qzs7MTl6qOkZ7jZrwLjy8AAABgHWSdF+YcdA6YOADasGGDyFXS53v4wjlFrH379nT48GGdXW68fMcBUXR0tPaY9evX6zwPH8P3M17q4116lY/h5UK+LR0DAAAA1s1W7iW5X375hRYsWCBqNXH+EF84z4idOnVK5A5xkjbXYuKSAo899piYHerdu7c4hssPcHD09NNP08GDB0UZgffee088tzQT9Morr4jnGjNmjKjxxDlSixcvppEjR2rHwktt3333nShZwOUNXn31VZF4PnToUJnODgAAACiKRkb846u6zJs3Tzx+8eJFTa9evTT+/v4aBwcHTUhIiGbw4MGapKQknec5c+aMOM7FxUXj6+ureeuttzTFxcU6x8THx2uaN2+ucXR01ERERGh/RmUzZ87UhIaGimPatGmj2blzp95/l8zMTDF2/goAAADqYMj7t6LqNKkZ6jQBAACoj2rrNAEAAAAoFYImAAAAAD0gaAIAAADQA4ImAAAAAD0gaAIAAADQA4ImAAAAAD0gaAIAAADQA4ImAAAAAD0gaAIAAADQg70+B8HdSYXVubIoAAAAqIP0vq1PgxQETUaSnZ0tvtatW1fuoQAAAEA13se5ncqdoPeckZSVldGlS5fIw8ODbGxsyBojdQ4Yz58/j957NYDzaBw4j8aB82gcOI/KPo8cBnHAFBwcTLa2d85awkyTkfCJDgkJIWvH/5Dxn0LN4TwaB86jceA8GgfOo3LP491mmCRIBAcAAADQA4ImAAAAAD0gaAKjcHJyokmTJomvUH04j8aB82gcOI/GgfNoOecRieAAAAAAesBMEwAAAIAeEDQBAAAA6AFBEwAAAIAeEDQBAAAA6AFBE1TbtGnTqHXr1qIKur+/Pw0YMICSk5PlHpbqffzxx6Kq/IgRI+QeiupcvHiRnnrqKapduza5uLhQ06ZNac+ePXIPS1VKS0tpwoQJFB4eLs5h/fr1afLkyXr15bJmmzdvpn79+omq0vz6Xbp0qc7jfP4mTpxIQUFB4rx2796djh8/Ltt41Xgei4uLaezYseJ17ebmJo4ZMmSI6MZhLgiaoNo2bdpEw4YNo507d9K6devEP+gePXpQbm6u3ENTrf/++4+++eYbatasmdxDUZ1r165Rx44dycHBgVatWkVHjx6lTz/9lGrVqiX30FTlk08+oa+//ppmzZpFiYmJ4vb06dNp5syZcg9N0fj/vdjYWJo9e3aVj/M5/PLLL2nOnDm0a9cu8abfs2dPKigoMPtY1Xoe8/LyaN++fSKo569//fWX+KD+4IMPmm+AXHIAwBjS0tL4o6hm06ZNcg9FlbKzszUNGjTQrFu3TtOlSxfNm2++KfeQVGXs2LGaTp06yT0M1evTp4/mueee07nv4Ycf1jz55JOyjUlt+P/BJUuWaG+XlZVpAgMDNTNmzNDed/36dY2Tk5Pmt99+k2mU6juPVdm9e7c47uzZsxpzwEwTGE1mZqb46uPjI/dQVIln7fr06SOm7cFwy5Yto1atWtFjjz0mlovj4uLou+++k3tYqtOhQwdav349HTt2TNw+ePAgbd26lXr16iX30FTr9OnTlJqaqvPa5l5nbdu2pR07dsg6Nkt437GxsSFvb2+z/Dw07AWjKCsrEzk4vDwSExMj93BUZ+HChWK6mZfnoHpOnTollpVGjRpF77zzjjiXb7zxBjk6OtIzzzwj9/BUY9y4caKbfOPGjcnOzk7kOH300Uf05JNPyj001eKAiQUEBOjcz7elx8BwvLTJOU6DBg0yWyNkBE1gtFmShIQE8YkUDHP+/Hl68803RV6Ys7Oz3MNRdeDOM01Tp04Vt3mmif9Ncg4Jgib9LV68mH799VdasGABNWnShA4cOCA+EHHSLc4jKAXn0D7++OMiwZ4/LJkLluegxoYPH04rVqyg+Ph4CgkJkXs4qrN3715KS0ujFi1akL29vbhwkj0njfJ1/qQPd8e7kqKjo3Xui4qKonPnzsk2JjUaPXq0mG0aOHCg2KX09NNP08iRI8VuWaiewMBA8fXy5cs69/Nt6TEwPGA6e/as+LBprlkmhqAJqo0jfA6YlixZQhs2bBBblMFw3bp1o8OHD4tP9NKFZ0x4OYSv8xIJ3B0vDd9c8oLzcurVqyfbmNSIdyjZ2uq+NfC/QZ7Jg+rh/xs5OOJcMQkvgfIuuvbt28s6NrUGTMePH6d///1XlBcxJyzPQY2W5HgK/++//xa1mqS1eU5w5DokoB8+dzfngfF2ZP7PAPlh+uPZEE5i5uU5/k919+7d9O2334oL6I9r5HAOU2hoqFie279/P3322Wf03HPPyT00RcvJyaETJ07oJH/zhx7eGMPnkpc4p0yZQg0aNBBBFG+b5yVPrm8H+p1Hnk1+9NFHRf4nr27wLLz0vsOPc/6iyZlljx5YJP7nU9Vl3rx5cg9N9VByoHqWL1+uiYmJEVu5GzdurPn222/lHpLqZGVliX97oaGhGmdnZ01ERITm3Xff1RQWFso9NEWLj4+v8v/DZ555Rlt2YMKECZqAgADx77Nbt26a5ORkuYetqvN4+vTp277v8PeZgw3/YfrQDAAAAEDdkNMEAAAAoAcETQAAAAB6QNAEAAAAoAcETQAAAAB6QNAEAAAAoAcETQAAAAB6QNAEAAAAoAcETQAAAAB6QNAEAGBCNjY2tHTpUrmHAQBGgKAJACzSs88+KwKWV155pcq+ifwYH2Ms77//PjVv3txozwcAyoOgCQAsVt26dWnhwoWUn5+vva+goEA0muYmqgAAhkDQBAAWq0WLFiJw+uuvv7T38XUOmOLi4rT3FRYW0htvvEH+/v7k7OxMnTp1ov/++0/7+MaNG8XM1Pr166lVq1bk6upKHTp0oOTkZPH4/Pnz6YMPPqCDBw+K4/jC90nS09PpoYceEt/HXe6XLVtmtnMAAMaDoAkALNpzzz1H8+bN096eO3cuDR06VOeYMWPG0J9//kk//vgj7du3jyIjI6lnz56UkZGhc9y7775Ln376Ke3Zs4fs7e3Fc7MnnniC3nrrLWrSpAmlpKSIC98n4YDq8ccfp0OHDlHv3r3pySefvOW5AUD5EDQBgEV76qmnaOvWrXT27Flx2bZtm7hPkpubS19//TXNmDGDevXqRdHR0fTdd9+Ri4sL/fDDDzrP9dFHH1GXLl3EMePGjaPt27eL5T4+1t3dXQRSgYGB4sL3STh3atCgQSIYmzp1KuXk5NDu3bvNeh4AoObsjfAcAACK5efnR3369BHLZRqNRlz39fXVPn7y5EkqLi6mjh07au9zcHCgNm3aUGJios5zNWvWTHs9KChIfE1LS7trflTl73NzcyNPT0/xfQCgLgiaAMDi8TLa8OHDxfXZs2dX+3k4mJJw3hIrKysz6Puk79Xn+wBAWbA8BwAW74EHHqCioiIxo8S5SpXVr1+fHB0dxbKdhI/jRHBehtMXP0dpaalRxw0AyoKZJgCweHZ2dtqlNr5eGS+XvfrqqzR69Gjy8fERS23Tp0+nvLw8ev755/X+GWFhYXT69Gk6cOAAhYSEkIeHBzk5ORn97wIA8kHQBABWgfOIbufjjz8Wy2VPP/00ZWdni7ICa9asoVq1aun9/I888ogoZ9C1a1e6fv262LFnzOKZACA/Gw1nRgIAAADAHSGnCQAAAEAPCJoAAAAA9ICgCQAAAEAPCJoAAAAA9ICgCQAAAEAPCJoAAAAA9ICgCQAAAEAPCJoAAAAA9ICgCQAAAEAPCJoAAAAA9ICgCQAAAIDu7v8Bof+XHPsbw6YAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Group DataFrame by saleMonth and then find the median SalePrice\n", "df_tmp.groupby([\"saleMonth\"])[\"SalePrice\"].median().plot()\n", "plt.xlabel(\"Month\")\n", "plt.ylabel(\"Median Sale Price ($)\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyzing State Distribution\n", "\n", "This code looks at how many bulldozers were sold in different states. We count the sales for each state and show the top 50. This information tells us:\n", "\n", "- Which states sell the most bulldozers\n", "- Which regions have the biggest markets"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["state\n", "Florida           67320\n", "Texas             53110\n", "California        29761\n", "Washington        16222\n", "Georgia           14633\n", "Maryland          13322\n", "Mississippi       13240\n", "Ohio              12369\n", "Illinois          11540\n", "Colorado          11529\n", "New Jersey        11156\n", "North Carolina    10636\n", "Tennessee         10298\n", "Alabama           10292\n", "Pennsylvania      10234\n", "South Carolina     9951\n", "Arizona            9364\n", "New York           8639\n", "Connecticut        8276\n", "Minnesota          7885\n", "Missouri           7178\n", "Nevada             6932\n", "Louisiana          6627\n", "Kentucky           5351\n", "Maine              5096\n", "Indiana            4124\n", "Arkansas           3933\n", "New Mexico         3631\n", "Utah               3046\n", "Unspecified        2801\n", "Wisconsin          2745\n", "New Hampshire      2738\n", "Virginia           2353\n", "Idaho              2025\n", "Oregon             1911\n", "Michigan           1831\n", "Wyoming            1672\n", "Iowa               1336\n", "Montana            1336\n", "Oklahoma           1326\n", "Nebraska            866\n", "West Virginia       840\n", "Kansas              667\n", "Delaware            510\n", "North Dakota        480\n", "Alaska              430\n", "Massachusetts       347\n", "Vermont             300\n", "South Dakota        244\n", "Hawaii              118\n", "Name: count, dtype: int64"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check the different values of different columns\n", "df_tmp.state.value_counts()[:50]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing Median Bulldozer Prices Across States\n", "\n", "This code makes a chart that shows how bulldozer prices differ from state to state. It helps us see:\n", "\n", "- What the chart shows:\n", "    - The typical (median) price of bulldozers in each state\n", "    - A red dotted line showing the typical price across all states\n", "- What we can learn:\n", "    - Which states have more expensive or cheaper bulldozers\n", "    - How each state's prices compare to the country's average"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Group DataFrame by saleMonth and then find the median SalePrice per state as well as across the whole dataset\n", "median_prices_by_state = df_tmp.groupby([\"state\"])[\"SalePrice\"].median() # this will return a pandas Series rather than a DataFrame\n", "median_sale_price = df_tmp[\"SalePrice\"].median()\n", "\n", "# Create a plot comparing median sale price per state to median sale price overall\n", "plt.figure(figsize=(10, 7))\n", "plt.bar(x=median_prices_by_state.index, # Because we're working with a Series, we can use the index (state names) as the x values\n", "        height=median_prices_by_state.values)\n", "plt.xlabel(\"State\")\n", "plt.ylabel(\"Median Sale Price ($)\")\n", "plt.xticks(rotation=90, fontsize=7);\n", "plt.axhline(y=median_sale_price, \n", "            color=\"red\", \n", "            linestyle=\"--\", \n", "            label=f\"Median Sale Price: ${median_sale_price:,.0f}\")\n", "plt.legend();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Saving Processed Data to CSV**\n", "\n", "This code saves our processed bulldozer data to a file. Here's what it does:\n", "\n", "- Main Goal:\n", "    - Saves our bulldozer data into a CSV file (a type of spreadsheet)\n", "    - Makes sure our data is saved correctly without errors\n", "- How it works:\n", "    - Checks for any problems while saving the file\n", "    - Tells us if the save was successful or if there was an error\n", "    - Keeps the file clean by not adding extra row numbers"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Changes successfully saved to TrainAndValid_processed.csv\n"]}], "source": ["import pandas as pd\n", "\n", "# Assuming df is your DataFrame with the processed data\n", "# df = ...\n", "\n", "try:\n", "    df.to_csv('TrainAndValid_processed.csv', index=False)\n", "    print(\"Changes successfully saved to TrainAndValid_processed.csv\")\n", "except Exception as e:\n", "    print(f\"An error occurred while saving the changes: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Conclusion and Next Steps**\n", "## Overview\n", "\n", "In this notebook, we worked on making our dataset better by adding new useful information. This will help our machine learning model make better predictions about bulldozer prices. Here's what we did:\n", "\n", "### Feature Creation\n", "\n", "- **Feature Creation**: We successfully engineered several new features, including:\n", "    - **Bulldozer Age**: We found out how old each bulldozer was by looking at when it was made and when it was sold.\n", "    - **Usage Information**: We looked at how much each bulldozer was used based on its working hours and similar data\n", "    - **Number and Text Conversions**: Changed information like model names and categories into numbers that our machine learning model can work with better\n", "\n", "### Data Processing\n", "\n", "- **Feature Selection**: We looked at how useful each piece of data was by checking how it relates to bulldozer prices and testing it in simple models.\n", "- **Data Transformation**:  Changed the data into a format that our machine learning model can better understand. This included adjusting numbers to be on similar scales and converting text data into numbers.\n", "\n", "### Results\n", "\n", "These steps have laid a solid foundation for building a robust predictive model by ensuring that the dataset is rich with relevant and informative features.\n", "\n", "## Next Steps\n", "- `03_exploratory_data_analysis.ipynb:` This notebook will contain the exploratory data analysis (EDA), including visualizing data distributions, relationships between variables, and identifying patterns.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "ZY3l0-AxO93d"}, "source": ["---"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Data Practitioner Jupyter Notebook.ipynb", "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}