Original message
Message ID	<<EMAIL>>
Created on:	17 April 2025 at 12:56 (Delivered after 38 seconds)
From:	Assessment Results <<EMAIL>>
To:	<EMAIL>
Subject:	Portfolio Project 5: Predictive Analytics Assessment Result | Fail
SPF:	PASS with IP ************* Learn more
DKIM:	'PASS' with domain codeinstitute.net Learn more
DMARC:	'PASS' Learn more


Download original	Copy to clipboard	
Delivered-To: <EMAIL>
Received: by 2002:adf:fa44:0:b0:391:211b:2160 with SMTP id y4csp879689wrr;
        Thu, 17 Apr 2025 03:56:51 -0700 (PDT)
X-Received: by 2002:a05:6808:158c:b0:3fa:ba79:6e63 with SMTP id 5614622812f47-400b022f4a4mr3484975b6e.30.1744887410947;
        Thu, 17 Apr 2025 03:56:50 -0700 (PDT)
ARC-Seal: i=1; a=rsa-sha256; t=1744887410; cv=none;
        d=google.com; s=arc-20240605;
        b=BFotax8RdzrsaKotNRiT0PVV3O2Kb73P/EbjoLPFH/hY4BAMSv3RJB7WglIHAKu1CM
         6/NTzeCC9V5TwXPQwLEiUW75R9yiphatsgmGg3RFGmDDsJdsY36+1yB7+CPlK5CB9ZOM
         4lOKCB6gipAegIodLyTDynNS65+CTWlgdMn0wLv/tXxEnXMJ7Z9wrw0rkbHwmwjQWnhT
         T4rpuAerSkQY0H/sFSTRSqwC88JZPFFTYVDsbsn8NWopBwgiCClDrpOj6syFALtU3eXU
         0CnTTopbxpMs+ExzV4fXenlz/LQAn7NDHwimeHJIZKytolHD2Ve0jmbzHYMuK3k54goA
         myzw==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=to:subject:message-id:date:from:reply-to:in-reply-to:references
         :mime-version:dkim-signature;
        bh=rq5HlsPsXeicEwoomPybDK4MMKdHbiAgscwDdnRBh5s=;
        fh=YgMy48niKEbMCdbcvO9uFgKXSbRVnPqp1Sx0rxrTNMc=;
        b=Ic3MRRUWdM+mTvqR3/cFvlSsjt1xIWrOGHvvEBpERNE10zkDG4g7OQfiOwXvDH95sJ
         M6ICFLqiWX0F6B9VGZiu2N+Qo0s35jNCMYrZkYerA3HyHGCE06eurIAcboOOJYpR7qwG
         4o5D1uySrktacm5RQXNAufbi7yepNlAzTSgNS55T6W3S1x4kOBRKl6mcERIpQpp14kxV
         19yG9X5Db+Zzu4MrLMsM+sWRYuri+8ndHloUnv58YAjAMZLIACCCFsaGEt8nMAHzU2Cy
         dA3N50QoMy4JqWGkpiEYf5h3gUxOcBSPc0crt98wfBTPFUKuw+Em+5GkC3SzbjFb+0y2
         9usg==;
        dara=google.com
ARC-Authentication-Results: i=1; mx.google.com;
       dkim=pass header.i=@codeinstitute.net header.s=google header.b=iDyF8Bia;
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=codeinstitute.net;
       dara=pass header.i=@gmail.com
Return-Path: <<EMAIL>>
Received: from mail-sor-f41.google.com (mail-sor-f41.google.com. [*************])
        by mx.google.com with SMTPS id 5614622812f47-4007d9693dcsor5092406b6e.11.2025.***********.50
        for <<EMAIL>>
        (Google Transport Security);
        Thu, 17 Apr 2025 03:56:50 -0700 (PDT)
Received-SPF: pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;
Authentication-Results: mx.google.com;
       dkim=pass header.i=@codeinstitute.net header.s=google header.b=iDyF8Bia;
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=codeinstitute.net;
       dara=pass header.i=@gmail.com
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=codeinstitute.net; s=google; t=1744887410; x=1745492210; dara=google.com;
        h=to:subject:message-id:date:from:reply-to:in-reply-to:references
         :mime-version:from:to:cc:subject:date:message-id:reply-to;
        bh=rq5HlsPsXeicEwoomPybDK4MMKdHbiAgscwDdnRBh5s=;
        b=iDyF8Bia93R3P8dyn4EAryz7C5bXa3f7nEgL0WskRKeJc20HsvbtOoJ2o/3lV2lIux
         Z+py3iqdgW3numsUFdWURM6GpQ39PdWPG80UhkCV0oryAQrvXNDoZ1iocsl7cIftJvUr
         8gmqKYzdIKBUANaxUudNB0A3kEUZUQ/wCUKbE=
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=1e100.net; s=20230601; t=1744887410; x=1745492210;
        h=to:subject:message-id:date:from:reply-to:in-reply-to:references
         :mime-version:x-gm-message-state:from:to:cc:subject:date:message-id
         :reply-to;
        bh=rq5HlsPsXeicEwoomPybDK4MMKdHbiAgscwDdnRBh5s=;
        b=JcGq+YyAt2b54E/4scCPRR4Ou1gTDfWgelsIS5/xC2n/l3UOS9h5xQlIjhOfION96z
         n/ICO5/FIuUxkCx4lkdF+GCcXX9upGIDj+iMJVaK1qZvOJMuLE9IFyyqX4b5uTjWvSfn
         +Sf9N9nJn3cMdYqszG4GzCiKp1F+YxvOI71Agu54bfldkUlmWqMC460fXKEnRSqL30D4
         ZChX+ieI3K8LTXI5EIyBJSj+IY+mxnrwgzEbwbhELU70lsDy6+pNwSQc4z6aluAeu+3f
         YYYoo3D1bUH0EeoVSZWAoK16Jv80/h5qnFAWHCjwp4bqGH46H0RA9lq/IEo/+TtW1D11
         Z6TQ==
X-Gm-Message-State: AOJu0Yx3VMmcPHFjIjnqaFWtUdws8SxQBRyW9qjMewc3Lp4khcChMjhM uAlxoYflTRIPjHx1W/JfD7msKxWtuG2wRP+p2Uuif1r4wbtd57Lnfc5NiiiLZpnLOTU3/Xo1eWj GUi1Uft+dX5cyt66YJvVSS3/uSDpSBGaFfzTfiICgR8KBGodhQrLrEisEVYKrzzPMZGjf7tWTvS KLA5aJ4l/OH1RSI7wC16ncKpdNthMV/Lj0rfc=
X-Gm-Gg: ASbGnct8P6GcT/iN6KzMFtk8LfwcQnzWYCaPf2rVNvtuI+Cz/QNL3x50VipQvmDOddS imJLoeeYO0Ko9HEdQH7xaU/tFKlzTFAlNu17JBzzIiyXqGtVNNEUDYlXrrOQ2WI0LbefNVMPe8i b8CAJSjtWwMlVIKjcEArz5vQ==
X-Google-Smtp-Source: AGHT+IF6EaaFQg64GK+msGnWKUBSDwiE/6qZp4ueMKYF8GVdfQ2pFL+cxtt1IeTh6auwHowRp8wr+3lBBSp9kgTenR8=
X-Received: by 2002:a05:6808:6904:b0:3f9:17f9:3097 with SMTP id 5614622812f47-400b01efbbcmr3811445b6e.23.1744887409732; Thu, 17 Apr 2025 03:56:49 -0700 (PDT)
MIME-Version: 1.0
References: <CAN2Zxfs1=<EMAIL>>
In-Reply-To: <CAN2Zxfs1=<EMAIL>>
Reply-To: <EMAIL>
From: Assessment Results <<EMAIL>>
Date: Thu, 17 Apr 2025 11:56:12 +0100
X-Gm-Features: ATxdqUFTLUvRDMjK0WbVO6XPrRA2orpucAHeWATP0Jz4KmBoPu0Moc0kwXTcGdU
Message-ID: <<EMAIL>>
Subject: Portfolio Project 5: Predictive Analytics Assessment Result | Fail
To: <EMAIL>
Content-Type: multipart/alternative; boundary="0000000000003b069c0632f7428d"

--0000000000003b069c0632f7428d
Content-Type: text/plain; charset="UTF-8"
Content-Transfer-Encoding: quoted-printable

Hi Johann,

The assessment of your Portfolio Project 5: Predictive Analytics has just
been completed.

The links you submitted were:

   - Deployed project
   <https://bulldozerpricegenius-707a4e3cbb84.herokuapp.com/>
   - Source code
   <https://github.com/Code-Institute-Submissions/About-BulldozerPriceGeniu=
s-_BPG-_v2>

You have received the following feedback from the assessor:

Please find the detailed mapping of criteria here.
<https://learn.codeinstitute.net/ci_program/diplomainsoftwaredevelopmenteco=
mmerce>
*LO1 - Use an Agile methodology to plan and design a Full-Stack Web
application using an MVC framework and related contemporary technologies:*
Criterion Meets Criterion Reason
1.1 Yes The Business requirements are articulated with a clear background
of the business problem along with objective dataset content descriptions
(preferably tabular)
Additional The project leverages the core principles of Artificial
Intelligence, Machine Learning (ML), and Data Science to predict bulldozer
price. The README document is structured with sections delineating dataset
content and presenting the three business requirements. The README also
outlines the features present in the database. To enhance clarity, it's
suggested to include specific details about the number of samples, sample
sizes for the train, validation, and test sets within the README. *LO2 -
Implement a data model, application features and business logic to manage,
query and manipulate data to meet given needs in a particular real-world
domain:*
Criterion Meets Criterion Reason
2.1 Yes Each Business requirement is categorized into Data Visualization
and Machine Learning tasks with the associated user stories in the
specified Format along with the use of Agile techniques and/or tools to map
the user stories
Additional The business requirements are given in a user story based
format. Moreover, the business requirements are supported with each of the
Data Visualization and ML Tasks. *LO3 - Identify and apply authorisation,
authentication and permission features in a Full-Stack web application
solution:*
Criterion Meets Criterion Reason
3.1 Yes Thematic data analysis was performed with self-explanatory Data
Visualization components. A consistent and logical storytelling approach
has been adopted for the analysis with excellent use of Ui elements to
facilitate clear understanding .
3.2 Yes A clear explanation was provided for the business case goals. ML
methods implemented and outcomes obtained are also documented along with
any edge cases and heuristics involved
3.3 Yes Audit trail of Git commits suggests educated usage of version
control - commit messages are well written and follow a consistent pattern
Additional This project employs effective plotting methodologies for data
analysis and visualization. The dashboard features importance plots,
parallel plots, and scatter plots to illustrate feature correlation with
price. The project defines hypotheses, validation methods, learning
approaches, outcome metrics, and model outputs, implementing ML Regression
using a Random Forest Regressor. Commits are generally focused on
individual functionalities. *LO4 - Create automated tests for a Full-Stack
Web application using an MVC framework and related contemporary
technologies:*
Criterion Meets Criterion Reason
4.1 No the "four_interactive_prediction" page currently only filters and
displays data from the training set based on user-selected price range and
state. This page should instead allow users to input feature values to
predict prices.
4.2 Yes Objective conclusions drawn with definitive data from the analysis
supporting the success/failure of the model/pipeline
Additional The data analytics task concluded with the ML model pipeline
achieving an R2 score of 0.836, as indicated on the dashboard. However, the
"four_interactive_prediction" page currently only filters and displays data
from the training set based on user-selected price range and state. This
page should instead allow users to input feature values to predict prices. =
*LO5
- Use a distributed version control system and a repository hosting service
to document, develop and maintain a Full-Stack Web application using an MVC
framework and related contemporary technologies.:*
Criterion Meets Criterion Reason
5.1 Yes Judicious use of Jupyter Notebooks with clear functional
connectivity to the app
5.2 Yes A Jupyter Notebook should be used for each major Analysis task with
clear subtext of the activities being performed
5.3 Yes Correct Deployment Setup for cloud hosting present
5.4 Yes Dashboards clearly implemented using Streamlit
Additional The modeling notebook helps to answer the business requirement
of prediction of the house price. The notebook contains tasks to define the
ML pipeline steps. The plot of Actual vs Prediction on the train and test
sets is not documented on the Dashboard. The R2 score is reported in the
Dashboard. Procfile, requirements.txt, runtime.txt, and setup.sh files are
present in the repo. The project implemented a dashboard using Streamlit. *=
LO6
- Deploy a Full-Stack Web application using an MVC framework and related
contemporary technologies to a cloud-based platform:*
Criterion Meets Criterion Reason
6.1 Yes Contents of each app page are clearly laid out with necessary
subtext and explanations for all pages
6.2 Yes The textual interpretation justifies the plots' purpose and the
significance of their results to the overarching business case
6.3 Yes A clear main menu present with all relevant pages incorporated
Additional A textual outline of each dashboard page is present in the
relavant section of the README. The navigation in the dashboard is good,
which enables user to navigate different sections of the project. Moreover,
a textual interpretation is provided for every plot (or set of plots) in
the dashboard. *LO7 - Understand and use object-based software concepts:*
Criterion Meets Criterion Reason
7.1 Yes Clear data collection mechanism implemented in Jupyter Notebooks
Additional The notebook for collecting the data from Kaggle is included in
the repo and the collection steps are clearly documented.

You have received the following textual feedback from the assessor for
*Merit* Criteria to give you an idea of what parts of the project were done
particularly well.
*Merit Criteria Comments: *
Criterion Meets Criterion Reason
1.2 Yes Clear articulation of project hypotheses with objective validation
processes to support them.
2.2 Yes Clear articulation of the ML tasks in the relevant section.
3.4 Yes Clear text section on all jupyter notebooks describing objectives,
inputs and outputs for the notebook. .
4.3 No The conclusions drawn need more objective evidence to prove their
validity
5.6 Yes Clear separation of concerns in the Streamlit app.
6.4 Yes There are at least 4 relevant plots in the dashboard to answer the
business requirements, with clear communication on their significant to the
business process
7.2 Yes Data cleaning and missing data imputation performed as per the
business requirements
Additional Many of the issues noted earlier will result in the requisite
merit criteria not being met here.

Overall comments from the assessor are as follows:

This project shows a lot of promise, and it=E2=80=99s clear that a great de=
al of
thought and effort has gone into the work. The Jupyter Notebooks are well
written and clearly structured, and there are some insightful
visualizations presented in the dashboard. The use of a random forest
regressor to predict bulldozer prices is a solid choice, and the ML
pipeline has achieved a commendable R=C2=B2 score of 0.836, which is clearl=
y
communicated in the dashboard. There is also evidence of good Git usage
throughout. Unfortunately, this project hasn=E2=80=99t quite met all the re=
quired
criteria to achieve a passing grade just yet. The main issue lies with the
"four_interactive_prediction" page, which currently filters and displays
training data based on user-selected price range and state. However, the
goal of this page should be to allow users to input feature values and
receive a predicted price in return. Addressing this functionality would
bring the project more in line with the original requirements.

We are sorry to inform you that your project submission has not been
successful in meeting the criteria above and has resulted in a *FAIL*
grade. Please note that the feedback you have received is personal to you
and is considered private so is not to be shared.

If the project has failed to meet any of the mandatory criteria which
result in an automatic fail, these would be listed below.
The project doesn't use one of the main technologies taught in the stream.

Could you kindly take a moment to complete this survey
<https://docs.google.com/forms/d/e/1FAIpQLSe5im6LJbdNokzmISlIBKlTkdrVFgGDzW=
84tBmhF6CJV3mJfg/viewform>
to share your feedback?

Thank you,
The Code Institute Assessment Team.

--=20
See our reviews on

=20
<https://www.trustpilot.com/review/codeinstitute.net?utm_medium=3DTrustbox&=
utm_source=3DEmailSignature1>

--0000000000003b069c0632f7428d
Content-Type: text/html; charset="UTF-8"
Content-Transfer-Encoding: quoted-printable

<div dir=3D"ltr"><div class=3D"gmail_quote gmail_quote_container">
<p>Hi Johann,</p>

<p>The assessment of your Portfolio Project 5: Predictive Analytics has jus=
t been completed.</p>

<p>The links you submitted were:
</p><ul>
<li><a href=3D"https://bulldozerpricegenius-707a4e3cbb84.herokuapp.com/" ta=
rget=3D"_blank">Deployed project</a></li>
<li><a href=3D"https://github.com/Code-Institute-Submissions/About-Bulldoze=
rPriceGenius-_BPG-_v2" target=3D"_blank">Source code</a></li>


</ul>
<p></p>

<p>You have received the following feedback from the assessor:</p>
<p> Please find the detailed mapping of criteria <a href=3D"https://learn.c=
odeinstitute.net/ci_program/diplomainsoftwaredevelopmentecommerce" target=
=3D"_blank">here.</a></p>

<b>LO1 - Use an Agile methodology to plan and design a Full-Stack Web appli=
cation using an MVC framework and related contemporary technologies:</b>
<table border=3D"1" cellpadding=3D"5" cellspacing=3D"0" width=3D"100%" styl=
e=3D"border-collapse:collapse">
  <tbody><tr>
    <th width=3D"25%">Criterion</th>
    <th width=3D"25%">Meets Criterion</th>
    <th width=3D"50%">Reason</th>
  </tr>
 =20
  <tr>
       <td>1.1</td>
       <td>Yes</td>
       <td>The Business requirements are articulated with a clear backgroun=
d of the business problem along with objective dataset content descriptions=
 (preferably tabular)</td>
 </tr>
=20
  <tr>
    <td>Additional</td>
  <td></td>
  <td>The project leverages the core principles of Artificial Intelligence,=
 Machine Learning (ML), and Data Science to predict  bulldozer price. The R=
EADME document is structured with sections delineating dataset content and =
presenting the three business requirements. The README also outlines the fe=
atures present in the database. To enhance clarity, it&#39;s suggested to i=
nclude specific details about the number of samples, sample sizes for the t=
rain, validation, and test sets within the README.
  </td></tr>

  </tbody></table><b>LO2 - Implement a data model, application features and=
 business logic to manage, query and manipulate data to meet given needs in=
 a particular real-world domain:</b>
<table border=3D"1" cellpadding=3D"5" cellspacing=3D"0" width=3D"100%" styl=
e=3D"border-collapse:collapse">
  <tbody><tr>
    <th width=3D"25%">Criterion</th>
    <th width=3D"25%">Meets Criterion</th>
    <th width=3D"50%">Reason</th>
  </tr>

   <tr>
       <td>2.1</td>
       <td>Yes</td>
       <td>Each Business requirement is categorized into Data Visualization=
 and Machine Learning tasks with the associated user stories in the specifi=
ed Format along with the use of Agile techniques and/or tools to map the us=
er stories</td>
 </tr>
=20
    <tr>
    <td>Additional</td>
  <td></td>
  <td>The business requirements are given in a user story based format. Mor=
eover, the business requirements are supported with each of the Data Visual=
ization and ML Tasks. </td>
  </tr> =20
 =20
  </tbody></table><b>LO3 - Identify and apply authorisation, authentication=
 and permission features in a Full-Stack web application solution:</b>
<table border=3D"1" cellpadding=3D"5" cellspacing=3D"0" width=3D"100%" styl=
e=3D"border-collapse:collapse">
  <tbody><tr>
    <th width=3D"25%">Criterion</th>
    <th width=3D"25%">Meets Criterion</th>
    <th width=3D"50%">Reason</th>
  </tr>
 =20
   <tr>
       <td>3.1</td>
       <td>Yes</td>
       <td>Thematic data analysis was performed with self-explanatory Data =
Visualization components. A consistent and logical storytelling approach ha=
s been adopted for the analysis with excellent use of Ui elements to facili=
tate clear understanding .</td>
 </tr>
=20
   <tr>
       <td>3.2</td>
       <td>Yes</td>
       <td>A clear explanation was provided for the business case goals. ML=
 methods implemented and outcomes obtained are also documented along with a=
ny edge cases and heuristics involved</td>
 </tr>
=20
   <tr>
       <td>3.3</td>
       <td>Yes</td>
       <td>Audit trail of Git commits suggests educated usage of version co=
ntrol - commit messages are well written and follow a consistent pattern</t=
d>
 </tr>

 <tr>
    <td>Additional</td>
  <td></td>
  <td>This project employs effective plotting methodologies for data analys=
is and visualization. The dashboard features importance plots, parallel plo=
ts, and scatter plots to illustrate feature correlation with price. The pro=
ject defines hypotheses, validation methods, learning approaches, outcome m=
etrics, and model outputs, implementing ML Regression using a Random Forest=
 Regressor. Commits are generally focused on individual functionalities.</t=
d>
 </tr> =20
 =20
  </tbody></table>
  <b>LO4 - Create automated tests for a Full-Stack Web application using an=
 MVC framework and related contemporary technologies:</b>
<table border=3D"1" cellpadding=3D"5" cellspacing=3D"0" width=3D"100%" styl=
e=3D"border-collapse:collapse">
  <tbody><tr>
    <th width=3D"25%">Criterion</th>
    <th width=3D"25%">Meets Criterion</th>
    <th width=3D"50%">Reason</th>
  </tr>
 =20
   <tr>
       <td>4.1</td>
       <td>No</td>
       <td> the &quot;four_interactive_prediction&quot; page currently only=
 filters and displays data from the training set based on user-selected pri=
ce range and state. This page should instead allow users to input feature v=
alues to predict prices.</td>
 </tr>
=20
   <tr>
       <td>4.2</td>
       <td>Yes</td>
       <td>Objective conclusions drawn with definitive data from the analys=
is supporting the success/failure of the model/pipeline</td>
 </tr>

 =20
 <tr>
    <td>Additional</td>
  <td></td>
  <td>The data analytics task concluded with the ML model pipeline achievin=
g an R2 score of 0.836, as indicated on the dashboard. However, the &quot;f=
our_interactive_prediction&quot; page currently only filters and displays d=
ata from the training set based on user-selected price range and state. Thi=
s page should instead allow users to input feature values to predict prices=
.</td>
 </tr> =20
 =20
  </tbody></table>
  <b>LO5 - Use a distributed version control system and a repository hostin=
g service to document, develop and maintain a Full-Stack Web application us=
ing an MVC framework and related contemporary technologies.:</b>
<table border=3D"1" cellpadding=3D"5" cellspacing=3D"0" width=3D"100%" styl=
e=3D"border-collapse:collapse">
  <tbody><tr>
    <th width=3D"25%">Criterion</th>
    <th width=3D"25%">Meets Criterion</th>
    <th width=3D"50%">Reason</th>
  </tr>
 =20
   <tr>
       <td>5.1</td>
       <td>Yes</td>
       <td>Judicious use of Jupyter Notebooks with clear functional connect=
ivity to the app</td>
 </tr>
=20
   <tr>
       <td>5.2</td>
       <td>Yes</td>
       <td>A Jupyter Notebook should be used for each major Analysis task w=
ith clear subtext of the activities being performed</td>
 </tr>
=20
   <tr>
       <td>5.3</td>
       <td>Yes</td>
       <td>Correct Deployment Setup for cloud hosting present</td>
 </tr>
   <tr>
       <td>5.4</td>
       <td>Yes</td>
       <td>Dashboards clearly implemented using Streamlit</td>
 </tr>

 =20
 <tr>
    <td>Additional</td>
  <td></td>
  <td>The modeling notebook helps to answer the business requirement of pre=
diction of the house price. The notebook contains tasks to define the ML pi=
peline steps. The plot of Actual vs Prediction on the train and test sets i=
s not documented on the Dashboard. The R2 score is reported in the Dashboar=
d. Procfile, requirements.txt, runtime.txt, and setup.sh files are present =
in the repo. The project implemented a dashboard using Streamlit. </td>
 </tr> =20
 =20
  </tbody></table>
  <b>LO6 - Deploy a Full-Stack Web application using an MVC framework and r=
elated contemporary technologies to a cloud-based platform:</b>
<table border=3D"1" cellpadding=3D"5" cellspacing=3D"0" width=3D"100%" styl=
e=3D"border-collapse:collapse">
  <tbody><tr>
    <th width=3D"25%">Criterion</th>
    <th width=3D"25%">Meets Criterion</th>
    <th width=3D"50%">Reason</th>
  </tr>
 =20

 <tr>
       <td>6.1</td>
       <td>Yes</td>
       <td>Contents of each app page are clearly laid out with necessary su=
btext and explanations for all pages</td>
 </tr>
=20
   <tr>
       <td>6.2</td>
       <td>Yes</td>
       <td>The textual interpretation justifies the plots&#39; purpose and =
the significance of their results to the overarching business case</td>
 </tr>
 <tr>
      <td>6.3</td>
      <td>Yes</td>
      <td>A clear main menu present with all relevant pages incorporated</t=
d>
</tr>
 =20
 <tr>
    <td>Additional</td>
  <td></td>
  <td>A textual outline of each dashboard page is present in the relavant s=
ection of the README. The navigation in the dashboard is good, which enable=
s user to navigate different sections of the project. Moreover, a textual i=
nterpretation is provided for every plot (or set of plots) in the dashboard=
.</td>
 </tr> =20
 =20
  </tbody></table>
  <b>LO7 - Understand and use object-based software concepts:</b>
<table border=3D"1" cellpadding=3D"5" cellspacing=3D"0" width=3D"100%" styl=
e=3D"border-collapse:collapse">
  <tbody><tr>
    <th width=3D"25%">Criterion</th>
    <th width=3D"25%">Meets Criterion</th>
    <th width=3D"50%">Reason</th>
  </tr>
 =20
 <tr>
      <td>7.1</td>
      <td>Yes</td>
      <td>Clear data collection mechanism implemented in Jupyter Notebooks<=
/td>
</tr>
 =20
 <tr>
    <td>Additional</td>
  <td></td>
  <td>The notebook for collecting the data from Kaggle is included in the r=
epo and the collection steps are clearly documented. </td>
 </tr> =20
 =20
  </tbody></table>
 =20

<p>You have received the following textual feedback from the assessor for <=
b>Merit</b> Criteria to give you an idea of what parts of the project were =
done particularly well.</p>

<b>Merit Criteria Comments: </b>
<table border=3D"1" cellpadding=3D"5" cellspacing=3D"0" width=3D"100%" styl=
e=3D"border-collapse:collapse">
  <tbody><tr>
    <th width=3D"25%">Criterion</th>
    <th width=3D"25%">Meets Criterion</th>
    <th width=3D"50%">Reason</th>
  </tr>
  <tr>
  <td>1.2</td>
   <td>Yes</td>
    <td>Clear articulation of project hypotheses with objective validation =
processes to support them. </td>
 </tr>
 <tr>
=20
   </tr><tr>
  <td>2.2</td>
   <td>Yes</td>
    <td>Clear articulation of the ML tasks in the relevant section.</td>
 </tr>
=20
  <tr>
  <td>3.4</td>
   <td>Yes</td>
    <td>Clear text section on all jupyter notebooks describing objectives, =
inputs and outputs for the notebook. . </td>
 </tr>
=20
   <tr>
  <td>4.3</td>
   <td>No</td>
    <td>The conclusions drawn need more objective evidence to prove their v=
alidity </td>
 </tr>
=20
   <tr>
  <td>5.6</td>
   <td>Yes</td>
    <td>Clear separation of concerns in the Streamlit app.</td>
 </tr>
=20
   <tr>
  <td>6.4</td>
   <td>Yes</td>
    <td>There are at least 4 relevant plots in the dashboard to answer the =
business requirements, with clear communication on their significant to the=
 business process</td>
 </tr>
 <tr>
 <td>7.2</td>
 <td>Yes</td>
 <td>Data cleaning and missing data imputation performed as per the busines=
s requirements=09</td>
  </tr>

    <tr>
    <td>Additional</td>
  <td></td>
  <td>Many of the issues noted earlier will result in the requisite merit c=
riteria not being met here.
  </td></tr>
   </tbody></table>

<p>Overall comments from the assessor are as follows:</p>
<p>This project shows a lot of promise, and it=E2=80=99s clear that a great=
 deal of thought and effort has gone into the work. The Jupyter Notebooks a=
re well written and clearly structured, and there are some insightful visua=
lizations presented in the dashboard. The use of a random forest regressor =
to predict bulldozer prices is a solid choice, and the ML pipeline has achi=
eved a commendable R=C2=B2 score of 0.836, which is clearly communicated in=
 the dashboard.
There is also evidence of good Git usage throughout. Unfortunately, this pr=
oject hasn=E2=80=99t quite met all the required criteria to achieve a passi=
ng grade just yet. The main issue lies with the &quot;four_interactive_pred=
iction&quot; page, which currently filters and displays training data based=
 on user-selected price range and state. However, the goal of this page sho=
uld be to allow users to input feature values and receive a predicted price=
 in return. Addressing this functionality would bring the project more in l=
ine with the original requirements.</p>

<p>We are sorry to inform you that your project submission has not been suc=
cessful in meeting the criteria above and has resulted in a <b>FAIL</b> gra=
de. Please note that the feedback you have received is personal to you and =
is considered private so is not to be shared.</p>
<p>If the project has failed to meet any of the mandatory criteria which re=
sult in an automatic fail, these would be listed below.</p>



The project doesn&#39;t use one of the main technologies taught in the stre=
am.


<p>Could you kindly take a moment to complete <a href=3D"https://docs.googl=
e.com/forms/d/e/1FAIpQLSe5im6LJbdNokzmISlIBKlTkdrVFgGDzW84tBmhF6CJV3mJfg/vi=
ewform" target=3D"_blank">this survey</a> to share your feedback?</p><p>
 Thank you,
  <br>
  The Code Institute Assessment Team.
</p>
</div></div>

<br>
<span style=3D"color:rgb(61,61,61);font-family:Arial,&quot;Arial Black&quot=
;,Tahoma,&quot;Trebuchet MS&quot;,Verdana,sans-serif;font-size:13px;letter-=
spacing:0.3px">See our reviews on</span><br style=3D"color:rgb(61,61,61);fo=
nt-family:Arial,&quot;Arial Black&quot;,Tahoma,&quot;Trebuchet MS&quot;,Ver=
dana,sans-serif;font-size:13px;letter-spacing:0.3px"><a href=3D"https://www=
.trustpilot.com/review/codeinstitute.net?utm_medium=3DTrustbox&amp;utm_sour=
ce=3DEmailSignature1" rel=3D"noopener noreferrer" style=3D"color:rgb(17,126=
,156);font-family:Arial,&quot;Arial Black&quot;,Tahoma,&quot;Trebuchet MS&q=
uot;,Verdana,sans-serif;font-size:13px;letter-spacing:0.3px" target=3D"_bla=
nk"><img src=3D"https://emailsignature.trustpilot.com/brand/s/1/logo.png" b=
order=3D"0" width=3D"82" height=3D"28" alt=3D"Trustpilot Logo" style=3D"max=
-width:82px;max-height:28px;width:82px"><br><img src=3D"https://emailsignat=
ure.trustpilot.com/signature/en-US/1/5c7d3cd27f012a000100d274/stars.png" bo=
rder=3D"0" width=3D"128" height=3D"24" alt=3D"Trustpilot Stars" style=3D"ma=
x-width:128px;max-height:24px;width:128px"></a>
--0000000000003b069c0632f7428d--