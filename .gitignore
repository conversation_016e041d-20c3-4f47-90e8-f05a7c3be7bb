# Streamlit secrets (contains Google Drive file ID)
.streamlit/secrets.toml

# Virtual environments (should never be in repository)
venv/
myenv/
env/
.env/

# Large data files (use external storage instead)
data/
*.csv
*.zip
*.7z

# Large model files (use external loading instead)
src/models/randomforest_regressor_best_RMSLE.pkl
# Allow small preprocessing components file for Heroku deployment
!src/models/preprocessing_components.pkl

# Excel files
*.xlsx
*.xls

# Python cache and compiled files
core.Microsoft*
core.mongo*
core.python*
env.py
__pycache__/
*.py[cod]
*.pyc
*.pyo
*.pyd
.Python

# Node modules
node_modules/

# GitHub workflows (if not needed)
.github/

# API keys and credentials
cloudinary_python.txt
kaggle.json
*.json

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db