bining: (1) Python packages for Machine Learning, Data Analysis and Data Visualisations; and (2) Streamlit for fast Machine Learning prototyping. This project allows you to perform critical data analysis to generate useful insights, and deliver data-driven recommendations.

The project immerses you into an environment that fully reflects professional business requirements. It achieves this by encouraging you to reflect on the “whys” and the “hows” of a Machine Learning system that delivers tangible value to your organisation. The UI and the data analysis are conducted in a way that is in line with the business requirements.

Describe and understand the project goals and how it is aligned with major analytics initiatives/programs in your organisation (LO1, LO2, LO3)
Formulate how the project’s stakeholders would benefit from the project and its deliverables (LO3)
Formulate Business Requirements that match needs and expectations from stakeholders and the organisation. (LO2, LO3).
Map the business requirements to the Data Visualisations and Machine Learning tasks (LO2, LO5, LO6)
Design the User Interface that matches business requirements (LO2, LO4, LO6)
Gather data and evaluate the need for cleaning and processing, so it can serve as the starting point for obtaining insights (LO4, LO7)
Create at least one Machine Learning pipeline that is embedded into an Application and has tangible evidence that the pipeline's prediction is used to solve a business requirement (LO1, <PERSON>O2, LO5).
Deliver insights using a narrative and Data Visualisation (LO4, LO6)
Build and deploy a Streamlit application for fast ML prototyping. (LO2, LO3, LO4, LO5, LO6, LO7)
Main Technologies
Required: Python, and its libraries related to Data Analysis and Machine Learning tasks covered in the course. In the project portfolio, you are required to use only Python as a programming language.

Project Ideas
You can either choose to build the website based on Project Idea 0 or take inspiration from the other example ideas below.

Project Idea 0
We invite you to bring your own idea(s) to life, based on providing value to users to address a specific real or imagined need.

You will need to source a dataset which will answer the Business Case Assessment, so you can use that information to build your project README file. If the dataset is not clearly stated as public, you must have the dataset owner’s permission to use it, as it will be hosted on a publicly available repository. The data should be anonymised to avoid any privacy concerns.

Project Idea 1: Mildew Detection in Cherry Leaves
Marianne McGuineys, a fictional individual, is the head of IT and Innovation at Farmy & Foods, a company in the agricultural sector that produces and harvests different types of food. Recently, she is facing a challenge where their cherry plantations have been presenting powdery mildew, which is a fungal disease that affects a wide range of plants.

The cherry plantation crop is one of their finest products in the portfolio and the company is concerned about supplying the market with a product of compromised quality.

Currently, the process is to manually verify if a given cherry tree contains powdery mildew. An employee spends around 30 minutes in each tree, taking a few samples of tree leaves and verifying visually if the leaf tree is healthy or has powdery mildew. If it has powdery mildew, the employee applies a specific compound to kill the fungus. The time spent applying this compound is 1 minute. The company has thousands of cherry trees located in multiple farms across the country. As a result, this manual process is not scalable due to time spent in the manual process inspection.

To save time in this process, the IT team suggested an ML system that is capable of detecting instantly, using a tree leaf image, if it is healthy or has powdery mildew. A similar manual process is in place for other crops for detecting pests, and if this initiative is successful, there is a realistic chance to replicate this project to all other crops. The dataset is a collection of cherry leaf images provided by Farmy & Foods, taken from their crops.

You will build a dashboard to detect if a cherry leaf is healthy or has powdery mildew. The business requirements are:

The client is interested in conducting a study to visually differentiate a cherry leaf that is healthy from one that contains powdery mildew.
The client is interested in predicting if a cherry tree is healthy or contains powdery mildew.
Deliver a dashboard that meets the above requirements.

Data source: https://www.kaggle.com/codeinstitute/cherry-leaves

Project Idea 2: Heritage Housing Issues
A fictional individual, Lydia Doe, has received an inheritance from a deceased great-grandfather. Included in the inheritance are four houses located in Ames, Iowa, USA. Although Lydia has an excellent understanding of property prices in her home country of Belgium, she fears that basing her estimates for property worth on her current knowledge of the Iowan market might lead to inaccurate appraisals. What makes a house desirable and valuable where she comes from might not be the same in Ames, Iowa.

Lydia needs help if she is to maximise the sales price for the inherited properties. She decides to ask a Data Practitioner for help. Her reasons for doing so are:

She doesn't know the worth of the properties and does not want to take the risk of inaccurate pricing estimation, since there is potentially a reasonable amount of money to be made or lost when selling the four properties.
She is also interested in predicting the sale price from any house in Ames, Iowa in case of future property ownership in that area.
From searching the Internet, Lydia found a public dataset with house prices for Ames, Iowa, and will provide you with that. You will build a Data Web App to predict the sales price from the four houses based on the house attributes. The business requirements are:

The client is interested in discovering how the house attributes correlate with the sale price. Therefore, the client expects data visualisations of the correlated variables against the sale price to show that.
The client is interested in predicting the house sales price from her four inherited houses, and any other house in Ames, Iowa.
Deliver a dashboard that meets the above requirements.

Data source: https://www.kaggle.com/codeinstitute/housing-prices-data

Assessment Criteria
Learning Outcomes
Learning Outcome	Description
LO1	Develop an understanding of the fundamentals of Artificial Intelligence, Machine Learning (ML) and Data Science.
LO2	Frame Machine Learning problems that map to business requirements to produce value.
LO3	Define the main business drivers that maximise the effectiveness in a Data Science project.
LO4	Obtain actionable insights using Data Analysis and manipulation.
LO5	Create intelligent systems using Machine Learning.
LO6	Represent data stories via Data Visualisation.
LO7	Collect, arrange and process data from one or more data sources.
Pass Performance
All Learning Outcomes mapped to the Milestone project, contain a set of criteria that are assessed throughout the project. Please find below all Learning Outcomes and their respective assessment criteria descriptions.

LO1: Develop an understanding of the fundamentals of Artificial Intelligence, Machine Learning (ML) and Data Science.
Criteria	Description
1.1	Demonstrate compliance with Business Understanding in terms of the CRISP-DM by describing the contents of the dataset and the business requirements.
LO2: Frame Machine Learning problems that map to business requirements to produce value.
Criteria	Description
2.1	Map the business requirements in a User Story based format to each of the Data Visualisation and ML Tasks along with the specific actions required for the enablement of each task.
2.2	Ensure at least 1 ML task is mentioned in the “Rationale to map the business requirements to the Data Visualisations and ML tasks” section in the README file.

LO3: Define the main business drivers that maximise the effectiveness in a Data Science project
Criteria	Description
3.1	Implement data analysis and data visualisation for the dataset using techniques covered in the course. See supporting information at the end of this section.
3.2	Articulate a Business Case for each Machine Learning task which must include the aim behind the predictive analytics task, the learning method, the ideal outcome for the process, success/failure metrics, model output and its relevance for the user, and any heuristics and training data used.
3.3	Use Git & GitHub for version control.

LO4: Obtain actionable insights using Data Analysis and manipulation.
Criteria	Description
4.1	Outline the conclusions of the data analytics task undertaken that helps answer a given business requirement in the appropriate section on the dashboard page.
4.2	Provide a clear statement on the dashboard to inform the user that the ML model/pipeline has been successful (or otherwise) in answering the predictive task it was intended to address.

LO5: Create intelligent systems using Machine Learning
Criteria	Description
5.1	Model the data to answer a business requirement using a Jupyter Notebook. See supporting information at the end of this section.
5.2	Evaluate, using a Jupyter Notebook, whether the ML Pipeline/Model meets the project requirements as defined in the ML Business Case. See supporting information at the end of this section.
5.3	Maintain Procfile, requirements.txt, runtime.txt, and setup.sh to enable the deployment at Heroku.
5.4	Implement a dashboard using Streamlit
5.5	Include a text section at the top of all Jupyter Notebooks, that describe the Objectives/Inputs/Outputs.
5.6	Implement “app_pages” and “src” folders to manage the dashboard pages for the application and other auxiliary tasks. See supporting information at the end of this section

LO6: Represent data stories via Data Visualisation.
Criteria	Description
6.1	Provide a textual outline of each dashboard page in the Dashboard Design section of the README file. See supporting information at the end of this section.
6.2	Provide a textual interpretation for every plot (or set of plots) in the dashboard. See supporting information at the end of this section.
6.3	Incorporate the main navigation menu in the dashboard that will answer the project requirements, using a structured layout.

LO7: Collect, arrange and process data from one or more data sources
Criteria	Description
7.1	Implement a data collection mechanism, from an endpoint, using Jupyter Notebook.
Supporting Information
Here you will find supporting information related to specific Pass Criteria:

Criteria	Supporting Information
3.1	
If it is an image dataset, it should contain notebooks with tasks to: set image shape, study for average and variability image; the difference between average images, image montage, plot number of images in train, validation, and test set.

If it is a tabular dataset - it should use for example pandas profiling; correlation and/or PPS study; it should visualise data by plotting relevant variables/correlations.

5.1	Your notebook should execute tasks, when applicable, for defining the ML pipeline steps; conducting hyperparameter optimisation; assessing feature importance; augmenting images and loading from folder to memory; defining neural network architecture; using techniques to prevent overfitting (such as early stopping and dropout layer), and fit the model/pipeline. Your modelling notebook has to create and save an ML pipeline/model that was fitted with the collected data.
5.2	If you have a tabular dataset, and if the ML task is regression, you should evaluate at least the R2 score as well as the plot Actual vs Prediction for both train and test sets. Or if the ML task is classification it should evaluate the confusion matrix and Scikit Learn classification report for both train and test sets. If it is an image dataset, it should indicate the learning curve (loss/accuracy) for both the train and validation sets. It should also evaluate and indicate the performance of the test set. Regardless of the case, it should be clearly stated in the notebook section where you evaluate the performance, whether or not your model/pipeline performance meets the performance requirement indicated in the business case
5.6	Other auxiliary tasks include, but are not limited to: handling and rendering predictions, and model evaluation on the dashboard.
6.1	List in bullet points the dashboard pages, and for each page: describe the content (such as text, plot, widgets etc) and, when applicable, indicate the business requirement that a given page is answering.
6.2	The plot should fit in with the scope of the dashboard page. It is not expected that a given plot is disconnected from the main purpose of that dashboard page. For example, consider the “Heritage Housing Issues” dataset has a “project summary” page and displays a disconnected histogram for SalePrice distribution.
Important: All Pass criteria must be achieved for a pass to be awarded.

It is expected that the project work submitted for this unit will demonstrate the same knowledge and skills shown in previous modules across the grading levels. The learner demonstrates characteristics of higher-level performance as described above.

Merit Performance
To evidence performance at the Merit level, a learner will, in general, demonstrate characteristics of performance at the Merit level as outlined below. The learner must achieve all Pass and Merit criteria for Merit to be awarded.

The learner has a clear rationale for the development of this project and has produced a fully functioning, well-documented Web Dashboard for a real-life audience.

The finished project has a clear, well-defined purpose addressing a particular target audience (or multiple related audiences). Its purpose would be immediately evident to a new user without having to look at supporting documentation. The Web Dashboard’s design follows the principles of UX design and accessibility guidelines, and the site is fully responsive.

The code is well-organised and easy to follow. The development process is evident through commit messages. The project’s documentation provides a clear rationale for the development of this project and covers all stages of the development life cycle.

The learner demonstrates characteristics of higher-level performance as described below.

Criteria	Description
1.2	Indicate the project hypothesis(es) and their validation process(es) in the README file.
2.3	Justify through statistical means that the hypotheses postulated as a part of the Business Requirements have been successfully met
4.3	Explain the conclusions from the project hypothesis(es) and the steps taken to validate it(them). See supporting information at the end of this section.
5.7	Document and Demonstrate the iterations of parameter tuning strategies or model selection strategies implemented to reach the final model used.
6.4	Implement at least 4 plots in your dashboard, either interactive or not, using libraries such as Matplotlib, Seaborn, or Plotly to answer the business requirements.
6.5	Include interactive visualisations, to allow the user to dynamically interact with graphical components.
7.2	Implement data preparation tasks, using Jupyter Notebook(s), to demonstrate compliance to the Data Preparation step from the CRISP-DM. See supporting information at the end of this section.
Supporting Information
Here you will find supporting information related to specific Merit Criteria

Criteria	Description
4.3	The rationale to validate the hypothesis(es) may be written either/both in the README or on a dashboard page. If applicable, list potential course of actions after your project hypothesis conclusions
7.2	The notebook(s) content will depend on whether your data is tabular or image-based. If the data is tabular you should have separate notebooks for data cleaning and feature engineering, where each should describe which tasks and techniques were applied. In the data cleaning notebook, you should clearly state how you investigated the missing levels and how you handled them. In the feature engineering notebook, your data should be cleaned and you should clearly state how you investigated which potential transformations could be applied to the data and how you validated if a given transformation may look reasonable to consider. If it is image-based, it should have tasks for removing non-image files, split train/validation/test folders.
Important: All Merit criteria must be achieved for a merit to be awarded.

Distinction Performance
At this level, a learner will have achieved all pass and merit criteria, as described above, and will demonstrate characteristics of high-level performance as described below:

The learner has documented a clear, justified, rationale for a real-world application and a comprehensive explanation of how it will be developed.
The finished project is judged to be publishable in its current form with a clearly evidenced professional-grade user interface and interaction adhering to current practice. There are no obvious errors in the code.
It clearly matches the design and demonstrates the characteristics of craftsmanship in the code. The resulting application is original and not a copy of any walkthrough projects encountered in the course.
The learning has to ensure proper ML terminology is used In the “ML Business Case” section at the README file, as described in the LMS section “Machine Learning Essentials” : “Machine Learning Terminology”.
The learner has to elaborate, either in the README or dashboard, and validate at least three hypotheses in the Project Hypothesis section at the README file.
The learner has to apply advanced modelling techniques, using a Jupyter Notebook, when modelling the data to meet a business requirement. Specifically for the present portfolio project, “advanced techniques” mean: In case you used Conventional ML for a tabular dataset, you are required to provide evidence that when conducting a hyperparameter optimisation in a given algorithm, you used at least 6 different hyperparameters and for each hyperparameter, and used at least 3 distinct acceptable values for each hyperparameter. You are required to explain the rationale when defining your hyperparameter choices. In case you used Neural Network for image classification, you can’t use Sigmoid as the activation function in the output layer. You have to explain your rationale when defining your network layers, compiling the model, and setting hyperparameters.
The learner has to display at least 4 different plot types in the dashboard, considering that each plot type must help to answer a business requirement.
The learner has to produce code, in the Jupyter Notebooks, that demonstrates that the files being pushed to the repository are stored under a dedicated folder that relates to a given project version.
Important: All Distinction criteria must be achieved for a distinction to be awarded.

Amplification (craftsmanship):

Design

The design of the web application demonstrates the main principles of good UX design:

Information Hierarchy

Semantic markup is used to convey structure - all information displayed on the site is presented in an organised fashion with each piece of information being easy to find.
All resources on the site are easy to find, allowing users to navigate the layout of the site intuitively.
Information is presented and categorised in terms of its priority
User Control

All interaction with the site would be likely to produce a positive emotional response within the user. This is down to the flow of information layout, use of colour, clear and unambiguous navigation structures and all interaction feedback.
When displaying media files, the site avoids aggressive automatic pop-ups and autoplay of audio; instead letting the user initiate and control such actions.
Consistency

evident across all pages/sections and covers interactivity as well as design
Confirmation

user and data actions are confirmed where appropriate, feedback is given at all times
Accessibility

there is evident conformity to accessibility guidelines across all pages/sections and in all interactivity
Any design decisions that contravene accepted user interaction, user experience design principles are identified and described (comments in code and/or a section in the README)

Development and Implementation

Code demonstrates characteristics of ‘clean code.’

Consistent and appropriate naming conventions within code and in file naming, e.g.

File names and class names are descriptive and consistent.
File structure

there is a clear separation between custom code and any external files (for example, library files are all inside a directory named 'libraries')
Comments

all custom code files include clear and relevant comments explaining the purpose of code segments
Robust code

no logic errors are found when running code
The full design is implemented, providing a good solution to the users' demands and expectations.

Real-world application

Clearly understandable site-specific content is used rather than Lorem Ipsum placeholder text.
All links to external pages open in a separate tab when clicked.
Any custom video content is fit for purpose.
Version control systems are used effectively:

All code is managed in git with commit messages that reflect the specific reason for the commit.
There is a separate, well-defined commit for each individual feature/fix.
There are no very large commits which make it harder to understand the development process and could lead the assessor to suspect plagiarism.
Development process is documented:

The purpose of the application is clearly described in the README - key project goals, target audience...
README
Recommended structure for a README.md file:

As a mandatory part of the submission, your project must have a readme file named README.md, in markdown format, that will describe all aspects of your project.

The link below provides an official example of an expected structure for your readme file

Example README.md template

NOTE: While it is a requirement for your README to be in English, we also strongly recommend that your site is in English too. The assessment will be conducted in English so to ensure that the functionality and navigation can be fully assessed, we recommend that the site is in English. If the site is in a language other than English, at a very minimum you should ensure you have correctly set the lang attribute in the HTML to the character code of the web apps content language. This will allow the browser to attempt a translation into English.

Plagiarism

Plagiarism, as defined by the Oxford dictionary is “the practice of taking someone else's work or ideas and passing them off as one's own.” It is a serious academic offence for which there are serious consequences.

It is acceptable to use and reference others' code however it is an academic plagiarism offence if any piece of work which is not entirely the student's own is not correctly referenced or acknowledged. All student projects submitted will be reviewed for plagiarism. This includes checking code comparison tools, plagiarism software, review of git commit history and other mechanisms.

It is the responsibility of each student to ensure that any direct or indirect inclusion of the work of others is fully and adequately acknowledged. We appreciate that plagiarism may be unintentional however it will still be treated as an offence. The Tutoring team can answer any plagiarism related queries, but as a general rule, if in doubt, include attribution of all sources.

Students are encouraged to ask mentors, tutors and their peers for advice about their project work but any submission should not include any code written by others, unless it is explicitly credited to them. Failure to correctly credit code that a student hasn't created themselves will be considered plagiarism and will result in a failing grade. Blatant or repeat offences of plagiarism will not be tolerated and will result in stringent penalties being applied, including removal from the course.

Assessment Policy
The programme is assessed by 5 milestone projects. Some modules have challenges for you to complete however these are not graded. All 5 projects must be separate submissions, meeting the specified project brief requirements. All 5 projects must be passed before the Diploma can be awarded.

Your project is due at 12.00 noon (Irish time) on the specified submission date. Please contact Student Care if you are unclear what your submission date is.

Any project not submitted on time will be considered a fail. The project will be accepted up to 10 days after the original submission date, subject to payment of a late submission fee of €150. The grade will also be capped at the pass mark. Code Institute reserves the right not to accept any projects submitted after 10 days. Repeated late submissions will not be tolerated.

Extenuating circumstances are unforeseen significant events which may affect your ability to complete the course/submit your projects on time. Such circumstances include serious illness, hospitalisation, immediate family bereavement etc.

Supporting documentation for such circumstances must be provided for the extenuating circumstance to be considered. Foreseen events such as holidays, work commitments, weddings etc. are not considered extenuating.​

If you find yourself in this situation, please contact Student Care as soon as is practical to discuss it. These instances are treated on a case by case basis. Supporting documentation e.g. medical certificate will be required for any additional time to be granted.

Please note, it will be assumed that you are proceeding with your studies as intended, until you advise us otherwise.

Completing your portfolio project
Your work on the portfolio project for this module is ready for submission once you've completed the following steps:

You have completed all of the functional requirements for the project work
You have deployed the project and documented the deployment
You have tested the project extensively and documented your testing
You have written sufficient overall documentation in the README.md file in English
You have had your final project review session with your mentor and addressed their feedback if any
You have checked you have met all assessment criteria for the learning outcomes
You are overall happy with your work
Grade Notification
Results are sent by email. The typical timeframe for receiving these from the date of submission are

Project 1 & 2: Up to 4 weeks
Project 3 & 4: Up to 6 weeks
Project 5: Up to 8 weeks. This result will be sent with your overall grade for the course.
In the unlikely event that you have not received your results within the above timeframes, please contact Student Care.

Resubmissions
If a project submission does not meet the required standard, you may resubmit your project to achieve a pass. Student Care will liaise with you to arrange the timeframe for a resubmission.

The grade for any project resubmitted will be capped at the pass mark. You will not be informed of the actual result. The maximum number of resubmissions per project is two. The resubmission fee is €50. A student whose project has received a pass mark is not permitted to resubmit their project with a view to achieving a higher grade.

Appeals
Where a student feels they have been unfairly marked or that the marking is inconsistent with the assessment criteria, they may appeal their grade. The appeal must be requested in writing to Student Care within 2 weeks of the grade being released. In doing so, you must also outline the grounds on which you are appealing the awarded grade. The project in its previously submitted format will be graded by a different assessor. You may not make any changes to your project (source code or live version) while the appeal process is taking place. The result of the appeal, which may be higher or lower than the original grade will be the final grade awarded. There is a €50 fee for the appeal which will be refunded if the outcome of the appeal is a higher passing grade. The appeal process will be completed within 5 weeks.

Final Grade Calculation
The five projects all contribute to your final grade. This is calculated from the points awarded for a pass/merit/distinction multiplied by the number of credit points per module. The full details of the calculation can be found in the full course Assessment Guide

Hi Johann,
The assessment of your Portfolio Project 5: Predictive Analytics has just been completed.
The links you submitted were:
Deployed project
Source code
You have received the following feedback from the assessor:
Please find the detailed mapping of criteria here.
LO1 - Use an Agile methodology to plan and design a Full-Stack Web application using an MVC framework
and related contemporary technologies:
Criterion Meets Criterion Reason
1.1 Yes
The Business requirements are articulated with a clear
background of the business problem along with objective
dataset content descriptions (preferably tabular)
Additional
The project leverages the core principles of Artificial
Intelligence, Machine Learning (ML), and Data Science
to predict bulldozer price. The README document is
structured with sections delineating dataset content and
presenting the three business requirements. The
README also outlines the features present in the
database. To enhance clarity, it's suggested to include
specific details about the number of samples, sample
sizes for the train, validation, and test sets within the
README.
LO2 - Implement a data model, application features and business logic to manage, query and manipulate data
to meet given needs in a particular real-world domain:
Criterion Meets Criterion Reason
2.1 Yes
Each Business requirement is categorized into Data
Visualization and Machine Learning tasks with the
associated user stories in the specified Format along
with the use of Agile techniques and/or tools to map the
user stories
Additional
The business requirements are given in a user story
based format. Moreover, the business requirements are
supported with each of the Data Visualization and ML
Tasks.
LO3 - Identify and apply authorisation, authentication and permission features in a Full-Stack web application
solution:
Criterion Meets Criterion Reason
3.1 Yes
Thematic data analysis was performed with selfexplanatory Data Visualization components. A consistent
and logical storytelling approach has been adopted for
the analysis with excellent use of Ui elements to facilitate
clear understanding .
3.2 Yes A clear explanation was provided for the business case
goals. ML methods implemented and outcomes obtained
6/29/25, 1:01 PM Gmail - Portfolio Project 5: Predictive Analytics Assessment Result | Fail
https://mail.google.com/mail/u/0/?ik=9d85ebafe2&view=pt&search=all&permmsgid=msg-f:1829647061624141866&simpl=msg-f:18296470616241… 1/4
are also documented along with any edge cases and
heuristics involved
3.3 Yes
Audit trail of Git commits suggests educated usage of
version control - commit messages are well written and
follow a consistent pattern
Additional
This project employs effective plotting methodologies for
data analysis and visualization. The dashboard features
importance plots, parallel plots, and scatter plots to
illustrate feature correlation with price. The project
defines hypotheses, validation methods, learning
approaches, outcome metrics, and model outputs,
implementing ML Regression using a Random Forest
Regressor. Commits are generally focused on individual
functionalities.
LO4 - Create automated tests for a Full-Stack Web application using an MVC framework and related
contemporary technologies:
Criterion Meets Criterion Reason
4.1 No
the "four_interactive_prediction" page currently only
filters and displays data from the training set based on
user-selected price range and state. This page should
instead allow users to input feature values to predict
prices.
4.2 Yes
Objective conclusions drawn with definitive data from the
analysis supporting the success/failure of the
model/pipeline
Additional
The data analytics task concluded with the ML model
pipeline achieving an R2 score of 0.836, as indicated on
the dashboard. However, the
"four_interactive_prediction" page currently only filters
and displays data from the training set based on userselected price range and state. This page should instead
allow users to input feature values to predict prices.
LO5 - Use a distributed version control system and a repository hosting service to document, develop and
maintain a Full-Stack Web application using an MVC framework and related contemporary technologies.:
Criterion Meets Criterion Reason
5.1 Yes
Judicious use of Jupyter Notebooks with clear functional
connectivity to the app
5.2 Yes
A Jupyter Notebook should be used for each major
Analysis task with clear subtext of the activities being
performed
5.3 Yes Correct Deployment Setup for cloud hosting present
5.4 Yes Dashboards clearly implemented using Streamlit
Additional
The modeling notebook helps to answer the business
requirement of prediction of the house price. The
notebook contains tasks to define the ML pipeline steps.
The plot of Actual vs Prediction on the train and test sets
is not documented on the Dashboard. The R2 score is
reported in the Dashboard. Procfile, requirements.txt,
runtime.txt, and setup.sh files are present in the repo.
The project implemented a dashboard using Streamlit.
LO6 - Deploy a Full-Stack Web application using an MVC framework and related contemporary technologies
to a cloud-based platform:
Criterion Meets Criterion Reason
6.1 Yes
Contents of each app page are clearly laid out with
necessary subtext and explanations for all pages
6.2 Yes
The textual interpretation justifies the plots' purpose and
the significance of their results to the overarching
business case
6/29/25, 1:01 PM Gmail - Portfolio Project 5: Predictive Analytics Assessment Result | Fail
https://mail.google.com/mail/u/0/?ik=9d85ebafe2&view=pt&search=all&permmsgid=msg-f:1829647061624141866&simpl=msg-f:18296470616241… 2/4
6.3 Yes
A clear main menu present with all relevant pages
incorporated
Additional
A textual outline of each dashboard page is present in
the relavant section of the README. The navigation in
the dashboard is good, which enables user to navigate
different sections of the project. Moreover, a textual
interpretation is provided for every plot (or set of plots) in
the dashboard.
LO7 - Understand and use object-based software concepts:
Criterion Meets Criterion Reason
7.1 Yes
Clear data collection mechanism implemented in Jupyter
Notebooks
Additional
The notebook for collecting the data from Kaggle is
included in the repo and the collection steps are clearly
documented.
You have received the following textual feedback from the assessor for Merit Criteria to give you an idea of what parts
of the project were done particularly well.
Merit Criteria Comments:
Criterion Meets Criterion Reason
1.2 Yes
Clear articulation of project hypotheses with objective
validation processes to support them.
2.2 Yes Clear articulation of the ML tasks in the relevant section.
3.4 Yes
Clear text section on all jupyter notebooks describing
objectives, inputs and outputs for the notebook. .
4.3 No The conclusions drawn need more objective evidence to
prove their validity
5.6 Yes Clear separation of concerns in the Streamlit app.
6.4 Yes
There are at least 4 relevant plots in the dashboard to
answer the business requirements, with clear
communication on their significant to the business
process
7.2 Yes
Data cleaning and missing data imputation performed as
per the business requirements
Additional Many of the issues noted earlier will result in the
requisite merit criteria not being met here.
Overall comments from the assessor are as follows:
This project shows a lot of promise, and it’s clear that a great deal of thought and effort has gone into the work. The
Jupyter Notebooks are well written and clearly structured, and there are some insightful visualizations presented in
the dashboard. The use of a random forest regressor to predict bulldozer prices is a solid choice, and the ML pipeline
has achieved a commendable R² score of 0.836, which is clearly communicated in the dashboard. There is also
evidence of good Git usage throughout. Unfortunately, this project hasn’t quite met all the required criteria to achieve
a passing grade just yet. The main issue lies with the "four_interactive_prediction" page, which currently filters and
displays training data based on user-selected price range and state. However, the goal of this page should be to allow
users to input feature values and receive a predicted price in return. Addressing this functionality would bring the
project more in line with the original requirements.
We are sorry to inform you that your project submission has not been successful in meeting the criteria above and has
resulted in a FAIL grade. Please note that the feedback you have received is personal to you and is considered
private so is not to be shared.
If the project has failed to meet any of the mandatory criteria which result in an automatic fail, these would be listed
below.
The project doesn't use one of the main technologies taught in the stream.
Could you kindly take a moment to complete this survey to share your feedback?