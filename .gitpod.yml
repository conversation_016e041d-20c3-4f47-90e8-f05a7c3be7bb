image:
  file: .gitpod.dockerfile
tasks:
  - init: . ${GITPOD_REPO_ROOT}/.vscode/init_tasks.sh
    command: /home/<USER>/.pg_ctl/bin/pg_start > /dev/null
  - command: . ${GITPOD_REPO_ROOT}/.vscode/uptime.sh &
vscode:
  extensions:
    - ms-python.python
    - formulahendry.auto-close-tag
    - eventyret.bootstrap-4-cdn-snippet
    - hookyqr.beautify
    - matt-rudge.auto-open-preview-panel
    - ms-toolsai.jupyter
    - ms-toolsai.jupyter-keymap
    - ms-toolsai.jupyter-renderers
